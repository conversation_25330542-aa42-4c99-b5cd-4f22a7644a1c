@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Fallback styles if Tailwind doesn't load */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-white { background-color: white; }
.bg-blue-500 { background-color: #3b82f6; }
.text-white { color: white; }
.text-gray-800 { color: #1f2937; }
.text-center { text-align: center; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.m-4 { margin: 1rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.w-full { width: 100%; }
.max-w-md { max-width: 28rem; }
.max-w-xs { max-width: 20rem; }
.cursor-pointer { cursor: pointer; }
.select-none { user-select: none; }
.fixed { position: fixed; }
.right-4 { right: 1rem; }
.top-half { top: 50%; }
.transform { transform: translateX(0) translateY(-50%); }
.hidden { display: none; }
@media (min-width: 1280px) {
  .xl-block { display: block; }
  .xl-hidden { display: none; }
}
.text-red-600 { color: #dc2626; }
.text-emerald-600 { color: #059669; }
.text-amber-600 { color: #d97706; }
.text-sky-600 { color: #0284c7; }
.bg-red-50 { background-color: #fef2f2; }
.bg-emerald-50 { background-color: #ecfdf5; }
.bg-amber-50 { background-color: #fffbeb; }
.bg-sky-50 { background-color: #f0f9ff; }
.font-medium { font-weight: 500; }
.relative { position: relative; }
.absolute { position: absolute; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-50 { z-index: 50; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.mt-2 { margin-top: 0.5rem; }
.gap-1 { gap: 0.25rem; }
.gap-3 { gap: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.border { border-width: 1px; }
.border-slate-200 { border-color: #e2e8f0; }
.border-slate-300 { border-color: #cbd5e1; }
.border-slate-600 { border-color: #475569; }
.hover\:bg-slate-100:hover { background-color: #f1f5f9; }
.hover\:bg-slate-700:hover { background-color: #334155; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
.transition-transform { transition-property: transform; }
.rotate-180 { transform: rotate(180deg); }
.text-sm { font-size: 0.875rem; }
.resize-none { resize: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5); }
.focus\:border-indigo-500:focus { border-color: #6366f1; }
.bg-indigo-600 { background-color: #4f46e5; }
.hover\:bg-indigo-700:hover { background-color: #4338ca; }
.flex-1 { flex: 1 1 0%; }

/* Directions for swipe actions */
.directions {
  margin: 2rem auto 1rem auto;
  max-width: 28rem;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.07);
  padding: 1.25rem 1.5rem;
  font-size: 1.1rem;
  color: #1f2937;
  text-align: center;
}
.directions-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}
.directions-list li {
  margin: 0.5rem 0;
  font-weight: 500;
}
.directions-left { color: #ef4444; }   /* red */
.directions-right { color: #22c55e; }  /* green */
.directions-down { color: #f59e42; }   /* orange */
.directions-up { color: #3b82f6; }     /* blue */
