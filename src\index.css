@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Fallback styles if Tailwind doesn't load */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-white { background-color: white; }
.bg-blue-500 { background-color: #3b82f6; }
.text-white { color: white; }
.text-gray-800 { color: #1f2937; }
.text-center { text-align: center; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.m-4 { margin: 1rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.w-full { width: 100%; }
.max-w-md { max-width: 28rem; }
.max-w-xs { max-width: 20rem; }
.max-w-2xl { max-width: 42rem; }
.cursor-pointer { cursor: pointer; }
.select-none { user-select: none; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.break-words { word-wrap: break-word; word-break: break-word; }
.leading-relaxed { line-height: 1.625; }
.fixed { position: fixed; }
.right-4 { right: 1rem; }
.top-half { top: 50%; }
.transform { transform: translateX(0) translateY(-50%); }
.hidden { display: none; }
@media (min-width: 1280px) {
  .xl-block { display: block; }
  .xl-hidden { display: none; }
}
.text-red-600 { color: #dc2626; }
.text-emerald-600 { color: #059669; }
.text-amber-600 { color: #d97706; }
.text-sky-600 { color: #0284c7; }
.bg-red-50 { background-color: #fef2f2; }
.bg-emerald-50 { background-color: #ecfdf5; }
.bg-amber-50 { background-color: #fffbeb; }
.bg-sky-50 { background-color: #f0f9ff; }
.font-medium { font-weight: 500; }
.relative { position: relative; }
.absolute { position: absolute; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-50 { z-index: 50; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.mt-2 { margin-top: 0.5rem; }
.gap-1 { gap: 0.25rem; }
.gap-3 { gap: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.border { border-width: 1px; }
.border-slate-200 { border-color: #e2e8f0; }
.border-slate-300 { border-color: #cbd5e1; }
.border-slate-600 { border-color: #475569; }
.hover\:bg-slate-100:hover { background-color: #f1f5f9; }
.hover\:bg-slate-700:hover { background-color: #334155; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
.transition-transform { transition-property: transform; }
.rotate-180 { transform: rotate(180deg); }
.text-sm { font-size: 0.875rem; }
.resize-none { resize: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5); }
.focus\:border-indigo-500:focus { border-color: #6366f1; }
.bg-indigo-600 { background-color: #4f46e5; }
.hover\:bg-indigo-700:hover { background-color: #4338ca; }
.flex-1 { flex: 1 1 0%; }

/* Enhanced slider styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  outline: none;
}

input[type="range"]::-webkit-slider-track {
  background: #e2e8f0;
  height: 12px;
  border-radius: 6px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: 3px solid #ffffff;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}

input[type="range"]::-moz-range-track {
  background: #e2e8f0;
  height: 12px;
  border-radius: 6px;
  border: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-moz-range-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: 3px solid #ffffff;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Color-specific slider styles */
.slider-red::-webkit-slider-thumb {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}
.slider-red::-webkit-slider-thumb:hover {
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}
.slider-red::-moz-range-thumb {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-amber::-webkit-slider-thumb {
  background: linear-gradient(135deg, #d97706, #f59e0b);
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}
.slider-amber::-webkit-slider-thumb:hover {
  box-shadow: 0 6px 16px rgba(217, 119, 6, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}
.slider-amber::-moz-range-thumb {
  background: linear-gradient(135deg, #d97706, #f59e0b);
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-emerald::-webkit-slider-thumb {
  background: linear-gradient(135deg, #059669, #10b981);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}
.slider-emerald::-webkit-slider-thumb:hover {
  box-shadow: 0 6px 16px rgba(5, 150, 105, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}
.slider-emerald::-moz-range-thumb {
  background: linear-gradient(135deg, #059669, #10b981);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-sky::-webkit-slider-thumb {
  background: linear-gradient(135deg, #0284c7, #0ea5e9);
  box-shadow: 0 4px 12px rgba(2, 132, 199, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}
.slider-sky::-webkit-slider-thumb:hover {
  box-shadow: 0 6px 16px rgba(2, 132, 199, 0.5), 0 2px 8px rgba(0, 0, 0, 0.15);
}
.slider-sky::-moz-range-thumb {
  background: linear-gradient(135deg, #0284c7, #0ea5e9);
  box-shadow: 0 4px 12px rgba(2, 132, 199, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode enhancements */
.dark input[type="range"]::-webkit-slider-track {
  background: #475569;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}
.dark input[type="range"]::-moz-range-track {
  background: #475569;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Additional utility classes for settings modal */
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.text-xs { font-size: 0.75rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.justify-center { justify-content: center; }
.text-center { text-align: center; }
.mb-3 { margin-bottom: 0.75rem; }
.mt-1 { margin-top: 0.25rem; }
.max-h-90vh { max-height: 90vh; }
.overflow-y-auto { overflow-y: auto; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }
.p-8 { padding: 2rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.border-t { border-top-width: 1px; }
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.text-slate-400 { color: #94a3b8; }
.text-slate-500 { color: #64748b; }
.text-slate-600 { color: #475569; }
.text-slate-700 { color: #334155; }
.hover\:bg-slate-50:hover { background-color: #f8fafc; }
.hover\:bg-slate-200:hover { background-color: #e2e8f0; }
.hover\:bg-slate-600:hover { background-color: #475569; }
.bg-slate-100 { background-color: #f1f5f9; }
.bg-slate-700 { background-color: #334155; }

/* Gradient backgrounds */
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.bg-clip-text { background-clip: text; }
.text-transparent { color: transparent; }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05); }
.group-hover\:scale-110:hover { transform: scale(1.1); }
.hover\:scale-\[1\.02\]:hover { transform: scale(1.02); }
.from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
.to-slate-50 { --tw-gradient-to: #f8fafc; }
.from-slate-800 { --tw-gradient-from: #1e293b; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(30, 41, 59, 0)); }
.to-slate-900 { --tw-gradient-to: #0f172a; }
.from-violet-50 { --tw-gradient-from: #f5f3ff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(245, 243, 255, 0)); }
.via-sky-50 { --tw-gradient-via: #f0f9ff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(240, 249, 255, 0)); }
.to-emerald-50 { --tw-gradient-to: #ecfdf5; }
.via-purple-900\/20 { --tw-gradient-via: rgba(88, 28, 135, 0.2); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(88, 28, 135, 0)); }
.from-violet-500 { --tw-gradient-from: #8b5cf6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(139, 92, 246, 0)); }
.to-purple-600 { --tw-gradient-to: #9333ea; }
.from-violet-600 { --tw-gradient-from: #7c3aed; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(124, 58, 237, 0)); }
.via-purple-600 { --tw-gradient-via: #9333ea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(147, 51, 234, 0)); }
.to-indigo-600 { --tw-gradient-to: #4f46e5; }
.from-violet-700 { --tw-gradient-from: #6d28d9; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(109, 40, 217, 0)); }
.via-purple-700 { --tw-gradient-via: #7c2d12; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(124, 45, 18, 0)); }
.to-indigo-700 { --tw-gradient-to: #3730a3; }
.from-indigo-500 { --tw-gradient-from: #6366f1; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(99, 102, 241, 0)); }
.from-indigo-600 { --tw-gradient-from: #4f46e5; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(79, 70, 229, 0)); }
.hover\:from-indigo-700:hover { --tw-gradient-from: #4338ca; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(67, 56, 202, 0)); }
.hover\:to-purple-700:hover { --tw-gradient-to: #7c3aed; }

/* Text gradients */
.bg-clip-text { background-clip: text; -webkit-background-clip: text; }
.text-transparent { color: transparent; }

/* Transform and transitions */
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.hover\:scale-\[1\.02\]:hover { --tw-scale-x: 1.02; --tw-scale-y: 1.02; }
.transition-all { transition-property: all; }
.duration-200 { transition-duration: 200ms; }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Color-specific backgrounds */
.from-red-50 { --tw-gradient-from: #fef2f2; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(254, 242, 242, 0)); }
.to-rose-100 { --tw-gradient-to: #fecaca; }
.from-amber-50 { --tw-gradient-from: #fffbeb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 251, 235, 0)); }
.to-orange-100 { --tw-gradient-to: #fed7aa; }
.from-emerald-50 { --tw-gradient-from: #ecfdf5; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 253, 245, 0)); }
.to-green-100 { --tw-gradient-to: #dcfce7; }
.from-sky-50 { --tw-gradient-from: #f0f9ff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(240, 249, 255, 0)); }
.to-blue-100 { --tw-gradient-to: #dbeafe; }

/* Dark mode gradient backgrounds */
.dark .from-red-950\/30 { --tw-gradient-from: rgba(69, 10, 10, 0.3); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(69, 10, 10, 0)); }
.dark .to-rose-950\/30 { --tw-gradient-to: rgba(76, 5, 25, 0.3); }
.dark .from-amber-950\/30 { --tw-gradient-from: rgba(69, 26, 3, 0.3); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(69, 26, 3, 0)); }
.dark .to-orange-950\/30 { --tw-gradient-to: rgba(67, 20, 7, 0.3); }
.dark .from-emerald-950\/30 { --tw-gradient-from: rgba(2, 44, 34, 0.3); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(2, 44, 34, 0)); }
.dark .to-green-950\/30 { --tw-gradient-to: rgba(5, 46, 22, 0.3); }
.dark .from-sky-950\/30 { --tw-gradient-from: rgba(8, 47, 73, 0.3); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(8, 47, 73, 0)); }
.dark .to-blue-950\/30 { --tw-gradient-to: rgba(23, 37, 84, 0.3); }

/* Directions for swipe actions */
.directions {
  margin: 2rem auto 1rem auto;
  max-width: 28rem;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.07);
  padding: 1.25rem 1.5rem;
  font-size: 1.1rem;
  color: #1f2937;
  text-align: center;
}
.directions-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}
.directions-list li {
  margin: 0.5rem 0;
  font-weight: 500;
}
.directions-left { color: #ef4444; }   /* red */
.directions-right { color: #22c55e; }  /* green */
.directions-down { color: #f59e42; }   /* orange */
.directions-up { color: #3b82f6; }     /* blue */

/* Violet/Purple theme colors */
.text-violet-600 { color: #7c3aed; }
.text-violet-400 { color: #a78bfa; }
.text-purple-200 { color: #e9d5ff; }
.bg-violet-100 { background-color: #ede9fe; }
.bg-purple-800\/50 { background-color: rgba(107, 33, 168, 0.5); }
.border-violet-200 { border-color: #ddd6fe; }
.border-purple-700 { border-color: #7e22ce; }
.border-violet-200\/50 { border-color: rgba(221, 214, 254, 0.5); }
.border-purple-700\/50 { border-color: rgba(126, 34, 206, 0.5); }
.focus\:ring-violet-500:focus { --tw-ring-color: #8b5cf6; }
.focus\:ring-purple-400:focus { --tw-ring-color: #c084fc; }

/* Code highlighting colors */
.text-blue-400 { color: #60a5fa; }
.text-green-400 { color: #4ade80; }
.text-yellow-400 { color: #facc15; }
.text-cyan-400 { color: #22d3ee; }
.text-gray-400 { color: #9ca3af; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-purple-400 { color: #c084fc; }
.text-orange-400 { color: #fb923c; }

/* CSS-specific gradient backgrounds */
.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }
.to-indigo-600 { --tw-gradient-to: #4f46e5; }

/* Prose styling */
.prose { color: #374151; max-width: 65ch; }
.prose-sm { font-size: 0.875rem; line-height: 1.7142857; }
.prose-base { font-size: 1rem; line-height: 1.75; }
.prose-invert { color: #d1d5db; }
.dark .prose-invert { color: #d1d5db; }

/* Typography utilities */
.italic { font-style: italic; }
.font-semibold { font-weight: 600; }
.font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }
.leading-relaxed { line-height: 1.625; }
.last\:mb-0:last-child { margin-bottom: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.leading-loose { line-height: 2; }
.p-6 { padding: 1.5rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.flex-shrink-0 { flex-shrink: 0; }
.mt-0\.5 { margin-top: 0.125rem; }
.w-2 { width: 0.5rem; }
.h-2 { height: 0.5rem; }
.bg-violet-500 { background-color: #8b5cf6; }
.rounded-full { border-radius: 9999px; }
.text-violet-700 { color: #6d28d9; }
.text-violet-600 { color: #7c3aed; }
.text-violet-300 { color: #c4b5fd; }
.text-violet-400 { color: #a78bfa; }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.h-48 { height: 12rem; }
.h-full { height: 100%; }
.max-h-64 { max-height: 16rem; }
.min-h-0 { min-height: 0; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.flex-shrink-0 { flex-shrink: 0; }
.rounded-lg { border-radius: 0.5rem; }
.bg-slate-50\/50 { background-color: rgba(248, 250, 252, 0.5); }
.bg-slate-800\/50 { background-color: rgba(30, 41, 59, 0.5); }
.p-1 { padding: 0.25rem; }
.p-4 { padding: 1rem; }
.block { display: block; }
.pr-2 { padding-right: 0.5rem; }
.-mr-2 { margin-right: -0.5rem; }

/* Custom scrollbar styling */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6, #9333ea);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #7c3aed, #7e22ce);
}

/* Better text wrapping */
.whitespace-pre-wrap {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
