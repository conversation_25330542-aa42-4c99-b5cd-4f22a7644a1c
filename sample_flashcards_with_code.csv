question,answer,code,category
"What is a constructor in Java?","🔹 **Key Features:**

1. Special method that initializes objects
2. Same name as the class
3. No return type (not even void)
4. Called automatically when object is created
5. Can be overloaded with different parameters

💡 **Types:**
• Default constructor (no parameters)
• Parameterized constructor (with parameters)","public class Student {
    private String name;
    private int age;
    
    // Default constructor
    public Student() {
        this.name = ""Unknown"";
        this.age = 0;
    }
    
    // Parameterized constructor
    public Student(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public void displayInfo() {
        System.out.println(""Name: "" + name + "", Age: "" + age);
    }
}","OOP"
"How do you create and use ArrayList in Java?","🔹 **Key Features:**

1. Dynamic array that can grow and shrink
2. Part of Java Collections Framework
3. Allows duplicate elements
4. Maintains insertion order
5. Not synchronized (not thread-safe)

💡 **Common Methods:**
• add() - Add elements
• get() - Retrieve elements
• remove() - Remove elements
• size() - Get number of elements","import java.util.ArrayList;

public class ArrayListExample {
    public static void main(String[] args) {
        // Create ArrayList
        ArrayList<String> fruits = new ArrayList<>();
        
        // Add elements
        fruits.add(""Apple"");
        fruits.add(""Banana"");
        fruits.add(""Orange"");
        
        // Access elements
        System.out.println(""First fruit: "" + fruits.get(0));
        
        // Remove element
        fruits.remove(""Banana"");
        
        // Iterate through list
        for (String fruit : fruits) {
            System.out.println(fruit);
        }
        
        System.out.println(""Size: "" + fruits.size());
    }
}","Collections"
"What is method overloading in Java?","🔹 **Definition:**
Method overloading allows multiple methods with the same name but different parameters in the same class.

🔹 **Rules:**
1. Same method name
2. Different parameter list (number, type, or order)
3. Return type can be same or different
4. Compile-time polymorphism

💡 **Benefits:**
• Code reusability
• Improved readability
• Flexibility in method calls","public class Calculator {
    // Method with 2 int parameters
    public int add(int a, int b) {
        return a + b;
    }
    
    // Method with 3 int parameters
    public int add(int a, int b, int c) {
        return a + b + c;
    }
    
    // Method with 2 double parameters
    public double add(double a, double b) {
        return a + b;
    }
    
    // Method with different parameter types
    public String add(String a, String b) {
        return a + b;
    }
    
    public static void main(String[] args) {
        Calculator calc = new Calculator();
        System.out.println(calc.add(5, 3));        // 8
        System.out.println(calc.add(1, 2, 3));     // 6
        System.out.println(calc.add(2.5, 3.7));    // 6.2
        System.out.println(calc.add(""Hello"", ""World"")); // HelloWorld
    }
}","OOP"
"How do you handle exceptions with try-catch in Java?","🔹 **Exception Handling:**
Mechanism to handle runtime errors gracefully without crashing the program.

🔹 **Key Components:**
1. try block - Code that might throw exception
2. catch block - Handles specific exceptions
3. finally block - Always executes (optional)
4. throw - Manually throw exceptions
5. throws - Declare exceptions in method signature

⚠️ **Best Practices:**
• Catch specific exceptions first
• Use finally for cleanup
• Don't catch Exception unless necessary","import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;

public class ExceptionExample {
    public static void main(String[] args) {
        try {
            // Code that might throw exception
            int result = divide(10, 0);
            System.out.println(""Result: "" + result);
            
            // File operation that might fail
            readFile(""nonexistent.txt"");
            
        } catch (ArithmeticException e) {
            System.out.println(""Cannot divide by zero: "" + e.getMessage());
        } catch (FileNotFoundException e) {
            System.out.println(""File not found: "" + e.getMessage());
        } catch (Exception e) {
            System.out.println(""General error: "" + e.getMessage());
        } finally {
            System.out.println(""Cleanup code always runs"");
        }
    }
    
    public static int divide(int a, int b) throws ArithmeticException {
        return a / b;
    }
    
    public static void readFile(String filename) throws FileNotFoundException {
        Scanner scanner = new Scanner(new File(filename));
        scanner.close();
    }
}","Error Handling"
"What is inheritance in Java?","🔹 **Definition:**
Mechanism where a new class inherits properties and methods from an existing class.

🔹 **Key Terms:**
• Parent/Super/Base class - Class being inherited from
• Child/Sub/Derived class - Class that inherits
• extends keyword - Used to inherit from a class
• super keyword - Reference to parent class

📋 **Benefits:**
• Code reusability
• Method overriding
• Polymorphism support
• Hierarchical classification","// Parent class
class Animal {
    protected String name;
    protected int age;
    
    public Animal(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public void eat() {
        System.out.println(name + "" is eating"");
    }
    
    public void sleep() {
        System.out.println(name + "" is sleeping"");
    }
}

// Child class inheriting from Animal
class Dog extends Animal {
    private String breed;
    
    public Dog(String name, int age, String breed) {
        super(name, age); // Call parent constructor
        this.breed = breed;
    }
    
    // Method overriding
    @Override
    public void eat() {
        System.out.println(name + "" the dog is eating dog food"");
    }
    
    // New method specific to Dog
    public void bark() {
        System.out.println(name + "" is barking: Woof!"");
    }
    
    public void displayInfo() {
        System.out.println(""Name: "" + name + "", Age: "" + age + "", Breed: "" + breed);
    }
}

public class InheritanceExample {
    public static void main(String[] args) {
        Dog myDog = new Dog(""Buddy"", 3, ""Golden Retriever"");
        myDog.displayInfo();
        myDog.eat();    // Overridden method
        myDog.sleep();  // Inherited method
        myDog.bark();   // Dog-specific method
    }
}","OOP"
"What are interfaces in Java?","🔹 **Definition:**
Contract that defines what methods a class must implement, without providing implementation details.

🔹 **Key Features:**
1. All methods are abstract by default (until Java 8)
2. Variables are public, static, final by default
3. Class implements interface using 'implements' keyword
4. Supports multiple inheritance
5. Can have default and static methods (Java 8+)

💡 **Use Cases:**
• Define contracts for classes
• Achieve multiple inheritance
• Loose coupling between classes","// Interface definition
interface Drawable {
    // Abstract method (implicitly public abstract)
    void draw();
    
    // Default method (Java 8+)
    default void display() {
        System.out.println(""Displaying the drawable object"");
    }
    
    // Static method (Java 8+)
    static void info() {
        System.out.println(""This is a Drawable interface"");
    }
}

interface Colorable {
    void setColor(String color);
}

// Class implementing multiple interfaces
class Circle implements Drawable, Colorable {
    private String color;
    private double radius;
    
    public Circle(double radius) {
        this.radius = radius;
    }
    
    @Override
    public void draw() {
        System.out.println(""Drawing a circle with radius: "" + radius);
    }
    
    @Override
    public void setColor(String color) {
        this.color = color;
        System.out.println(""Circle color set to: "" + color);
    }
}

public class InterfaceExample {
    public static void main(String[] args) {
        Circle circle = new Circle(5.0);
        circle.draw();
        circle.setColor(""Red"");
        circle.display(); // Default method
        Drawable.info();  // Static method
    }
}","OOP"
"What is polymorphism in Java?","🔹 **Definition:**
Ability of objects of different types to be treated as instances of the same type through a common interface.

🔹 **Types:**
1. **Compile-time polymorphism** - Method overloading
2. **Runtime polymorphism** - Method overriding

🔹 **Key Concepts:**
• Dynamic method dispatch
• Late binding
• Method overriding
• Interface implementation

📋 **Benefits:**
• Code flexibility
• Extensibility
• Maintainability","// Base class
abstract class Shape {
    protected String color;
    
    public Shape(String color) {
        this.color = color;
    }
    
    // Abstract method - must be overridden
    public abstract double calculateArea();
    
    // Concrete method
    public void displayColor() {
        System.out.println(""Color: "" + color);
    }
}

// Derived classes
class Rectangle extends Shape {
    private double width, height;
    
    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }
    
    @Override
    public double calculateArea() {
        return width * height;
    }
}

class Circle extends Shape {
    private double radius;
    
    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }
    
    @Override
    public double calculateArea() {
        return Math.PI * radius * radius;
    }
}

public class PolymorphismExample {
    public static void main(String[] args) {
        // Polymorphism in action
        Shape[] shapes = {
            new Rectangle(""Blue"", 5, 3),
            new Circle(""Red"", 4),
            new Rectangle(""Green"", 2, 8)
        };
        
        // Same method call, different behavior
        for (Shape shape : shapes) {
            shape.displayColor();
            System.out.println(""Area: "" + shape.calculateArea());
            System.out.println(""---"");
        }
    }
}","OOP"
