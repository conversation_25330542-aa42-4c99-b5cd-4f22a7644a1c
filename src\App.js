import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Brain, CheckCircle, Filter, Upload, Settings, Plus, Moon, Sun, ChevronLeft, ChevronRight
} from 'lucide-react';
import <PERSON> from 'papaparse';
import './index.css';

// --- FSRS Implementation ---
class FSRS {
  schedule(card, grade, now = new Date()) {
    const newCard = { ...card };
    newCard.lastReview = now.toISOString();
    newCard.reviewCount = (newCard.reviewCount || 0) + 1;

    switch (grade) {
      case 1: // Again
        newCard.difficulty = Math.min(10, newCard.difficulty + 0.8);
        newCard.stability = Math.max(0.1, newCard.stability * 0.8);
        newCard.dueDate = new Date(now.getTime() + 60000).toISOString(); // 1 minute
        break;
      case 2: // Hard
        newCard.difficulty = Math.min(10, newCard.difficulty + 0.15);
        newCard.stability = newCard.stability * 1.2;
        newCard.dueDate = new Date(now.getTime() + 86400000).toISOString(); // 1 day
        break;
      case 3: // Good
        newCard.difficulty = Math.max(1, newCard.difficulty - 0.1);
        newCard.stability = newCard.stability * 2.5;
        newCard.dueDate = new Date(now.getTime() + 259200000).toISOString(); // 3 days
        break;
      case 4: // Easy
        newCard.difficulty = Math.max(1, newCard.difficulty - 0.2);
        newCard.stability = newCard.stability * 3.0;
        newCard.dueDate = new Date(now.getTime() + 604800000).toISOString(); // 7 days
        break;
      default:
        break;
    }

    newCard.state = 'review';
    return newCard;
  }
}

// --- Initial Cards Data ---
const initialCardsData = [
  {
    id: 1,
    question: "What is a class in Java?",
    answer: "🔹 **Key Features:**\n\n1. A blueprint or template for creating objects\n2. Encapsulates data (fields) and methods\n3. Serves as a template for object creation\n4. Supports inheritance and polymorphism\n\n💡 **Example:**\n\nclass Car {\n    String brand;\n    void start() {\n        System.out.println(\"Car started\");\n    }\n}\n\nThis demonstrates how \\n creates new lines in the answer display.",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 2,
    question: "What is the difference between == and .equals() in Java?",
    answer: "🔹 **Key Differences:**\n\n1. == compares references (memory addresses)\n2. .equals() compares actual content\n3. For primitives, == compares values directly\n4. For objects, == compares memory addresses\n\n💡 **Example:**\n\nString a = new String(\"hello\");\nString b = new String(\"hello\");\nSystem.out.println(a == b);        // false\nSystem.out.println(a.equals(b));   // true",
    category: "Basic Concepts",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  }
];

// --- JavaCodeHighlighter Component ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Careful text preprocessing - format headers and add spacing to numbered lists
  const preprocessText = (text) => {
    return text
      .trim()

      // Only format very clear headers that are already properly formatted
      .replace(/^(Key\s+(?:points?|features?|benefits?|concepts?|principles?)):\s*$/gmi, '🔹 **$1:**')
      .replace(/^(Examples?):\s*$/gmi, '💡 **$1:**')
      .replace(/^(Important|Note|Remember|Warning):\s*$/gmi, '⚠️ **$1:**')
      .replace(/^(Summary|Conclusion):\s*$/gmi, '📋 **$1:**')
      .replace(/^(Definition):\s*$/gmi, '📖 **$1:**')
      .replace(/^(Syntax):\s*$/gmi, '⚡ **$1:**')

      // Add spacing before numbered lists (only when clearly a list item at start of line)
      .replace(/\n(\d+\.\s+)/g, '\n\n$1')

      // Add spacing before lettered lists
      .replace(/\n([a-zA-Z]\.\s+)/g, '\n\n$1')

      // Add spacing before bullet points (only clear bullet points)
      .replace(/\n([-•*]\s+)/g, '\n\n$1')

      // Clean up excessive newlines
      .replace(/\n\s*\n\s*\n+/g, '\n\n')

      // Clean up leading newlines
      .replace(/^\n+/, '');
  };

  // Check if content looks like code
  const looksLikeCode = (text) => {
    const strongCodePatterns = [
      /\bpublic\s+class\b|\bprivate\s+\w+\b|\bpublic\s+static\s+void\b/,
      /\w+\s*\([^)]*\)\s*\{/,
      /System\.out\.println\b|System\.err\.println\b/,
      /import\s+[\w.]+;/,
      /\bclass\s+\w+\s*\{/,
      /\bif\s*\([^)]+\)\s*\{|\bfor\s*\([^)]+\)\s*\{|\bwhile\s*\([^)]+\)\s*\{/,
    ];

    const weakCodePatterns = [
      /\{[\s\S]*\}/,
      /;[\s]*$/m,
      /\w+\.\w+\(/,
    ];

    const hasStrongCode = strongCodePatterns.some(pattern => pattern.test(text));
    const weakIndicatorCount = weakCodePatterns.filter(pattern => pattern.test(text)).length;
    const hasMultipleWeakIndicators = weakIndicatorCount >= 2 && text.length > 50;

    return hasStrongCode || hasMultipleWeakIndicators;
  };

  // Detect if content is CSS
  const looksLikeCSS = (text) => {
    const cssIndicators = [
      /\.[\w-]+\s*\{[\s\S]*?\}/,
      /#[\w-]+\s*\{[\s\S]*?\}/,
      /@media\s*\([^)]+\)\s*\{/,
      /@import\s+["']/,
      /@keyframes\s+[\w-]+\s*\{/,
      /\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];

    const indicatorCount = cssIndicators.filter(pattern => pattern.test(text)).length;
    const strongCSSPatterns = [
      /@media\s*\([^)]+\)/,
      /@import\s+/,
      /@keyframes\s+/,
      /\.\w+\s*\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];

    const hasStrongCSS = strongCSSPatterns.some(pattern => pattern.test(text));
    return hasStrongCSS || indicatorCount >= 2;
  };

  const isCode = looksLikeCode(code);
  const isCSS = looksLikeCSS(code);

  if (isCode) {
    // Code highlighting logic would go here
    const codeType = isCSS ? 'CSS' : 'Java';

    return (
      <div className={`bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-slate-600/50 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ${className}`}>
        <div className={`${isCSS ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700' : 'bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700'} px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg`}>
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">💻</span>
            {codeType} Code
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          <pre className="overflow-x-auto text-base font-mono leading-loose whitespace-pre-wrap h-full">
            <code className="text-gray-100 block preserve-linebreaks" style={{ wordBreak: 'normal', overflowWrap: 'normal', whiteSpace: 'pre-wrap', lineHeight: '1.9', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>
              {code}
            </code>
          </pre>
        </div>
      </div>
    );
  } else {
    // Regular text formatting with enhanced visual appeal
    const formatTextContent = (text) => {
      const processedText = preprocessText(text);
      const paragraphs = processedText.split(/\n\s*\n/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        // Enhanced header detection with emoji support
        const isHeader = /^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*.*\*\*:?\s*$/gi.test(cleanParagraph);
        if (isHeader) {
          const match = cleanParagraph.match(/^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*(.*?)\*\*:?\s*$/);
          if (match) {
            const [, emoji, headerText] = match;
            const getHeaderStyle = (emoji) => {
              switch (emoji) {
                case '💡': return 'from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 border-yellow-500';
                case '⚠️': return 'from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 border-red-500';
                case '📋': return 'from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-500';
                case '📖': return 'from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 border-green-500';
                case '⚡': return 'from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 border-purple-500';
                default: return 'from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 border-violet-500';
              }
            };

            return (
              <div key={pIndex} className="mb-8 last:mb-0">
                <div className={`bg-gradient-to-r ${getHeaderStyle(emoji)} rounded-xl p-5 border-l-4 shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100 flex items-center gap-4">
                    <span className="text-3xl drop-shadow-sm">{emoji}</span>
                    <span className="flex-1">{headerText.trim()}</span>
                  </h3>
                </div>
              </div>
            );
          }
        }

        // Check for numbered/lettered lists
        const isListItem = /^(\d+\.|[a-zA-Z]\.|[-•*])\s+/.test(cleanParagraph);

        if (isListItem) {
          const match = cleanParagraph.match(/^(\d+\.|[a-zA-Z]\.|[-•*])\s+(.*)$/);
          if (match) {
            const [, prefix, content] = match;
            return (
              <div key={pIndex} className="mb-5 last:mb-0 group">
                <div className="flex items-start gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-50/80 to-white/80 dark:from-slate-800/50 dark:to-slate-700/50 border border-slate-200/60 dark:border-slate-600/40 shadow-sm hover:shadow-md hover:from-violet-50/80 hover:to-purple-50/80 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300">
                  <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-sm">
                      {prefix.replace(/[.*]/, '•')}
                    </span>
                  </div>
                  <div className="flex-1 pt-1">
                    <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                      {content}
                    </p>
                  </div>
                </div>
              </div>
            );
          }
        }

        // Regular paragraph with enhanced styling
        return (
          <div key={pIndex} className="mb-6 last:mb-0">
            <div className="p-5 rounded-xl bg-gradient-to-br from-slate-50/90 to-white/90 dark:from-slate-800/60 dark:to-slate-700/60 border border-slate-200/70 dark:border-slate-600/50 shadow-sm hover:shadow-md transition-all duration-300">
              <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                {cleanParagraph}
              </p>
            </div>
          </div>
        );
      }).filter(Boolean);
    };

    return (
      <div className={`h-full overflow-y-auto p-4 answer-content preserve-linebreaks ${className}`}>
        {formatTextContent(code)}
      </div>
    );
  }
};

function App() {
  const [cards, setCards] = useState(() => {
    const saved = localStorage.getItem('flashcards');
    return saved ? JSON.parse(saved) : initialCardsData;
  });
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [fsrs] = useState(() => new FSRS());
  const [darkMode, setDarkMode] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);

  // Save cards to localStorage whenever cards change
  useEffect(() => {
    localStorage.setItem('flashcards', JSON.stringify(cards));
  }, [cards]);

  // Get unique categories
  const getCategories = useMemo(() => {
    const categories = ['All', ...new Set(cards.map(card => card.category))];
    return categories;
  }, [cards]);

  // Filter cards based on selected category and due date
  const studyDeck = useMemo(() => {
    const now = new Date();
    return cards.filter(card => {
      const categoryMatch = selectedCategory === 'All' || card.category === selectedCategory;
      const isDue = !card.dueDate || new Date(card.dueDate) <= now;
      return categoryMatch && isDue;
    });
  }, [cards, selectedCategory]);

  // Reset card index when study deck changes
  useEffect(() => {
    setCurrentCardIndex(0);
  }, [studyDeck.length]);

  // Reset showAnswer when card changes
  useEffect(() => {
    setShowAnswer(false);
  }, [currentCardIndex]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const handleGrade = useCallback((cardId, grade) => {
    const now = new Date();
    setCards(prevCards =>
      prevCards.map(card =>
        card.id === cardId ? fsrs.schedule(card, grade, now) : card
      )
    );
    setCurrentCardIndex(prev => prev + 1);
  }, [fsrs]);

  // Navigation functions
  const goToPreviousCard = useCallback(() => {
    setCurrentCardIndex(prev => Math.max(0, prev - 1));
  }, []);

  const goToNextCard = useCallback(() => {
    setCurrentCardIndex(prev => Math.min(studyDeck.length - 1, prev + 1));
  }, [studyDeck.length]);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;
      if (!currentDisplayCard) return;

      // Space or Enter to show answer
      if ((e.key === ' ' || e.key === 'Enter') && !showAnswer) {
        e.preventDefault();
        setShowAnswer(true);
        return;
      }

      // Number keys for grading (only when answer is shown)
      if (showAnswer && ['1', '2', '3', '4'].includes(e.key)) {
        e.preventDefault();
        const grade = parseInt(e.key);
        handleGrade(currentDisplayCard.id, grade);
      }

      // Arrow keys for navigation
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        goToPreviousCard();
      }
      if (e.key === 'ArrowRight') {
        e.preventDefault();
        goToNextCard();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentDisplayCard, showAnswer, handleGrade, goToPreviousCard, goToNextCard]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-sky-50 to-emerald-50 dark:from-slate-900 dark:via-purple-900/20 dark:to-slate-900 text-gray-800 dark:text-slate-200 flex flex-col items-center justify-center p-4 transition-all duration-500">
      <header className="w-full max-w-4xl mb-8 z-10">
        <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-violet-200/50 dark:border-purple-700/50">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl shadow-lg">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">JavaBrain Boost</h1>
                <p className="text-sm text-slate-500 dark:text-slate-400">Master Java with spaced repetition</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => alert('Add card functionality coming soon!')}
                title="Add New Card"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Plus className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.csv';
                  input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                      Papa.parse(file, {
                        header: true,
                        complete: (results) => {
                          const newCards = results.data
                            .filter(row => row.question && row.answer)
                            .map((row, index) => ({
                              id: Math.max(...cards.map(c => c.id), 0) + index + 1,
                              question: row.question || '',
                              answer: row.answer || '',
                              category: row.category || 'Imported',
                              difficulty: 5,
                              stability: 2.5,
                              state: 'new',
                              reviewCount: 0,
                              lastReview: null,
                              dueDate: null
                            }));

                          if (newCards.length > 0) {
                            setCards(prevCards => [...prevCards, ...newCards]);
                            alert(`Successfully imported ${newCards.length} cards!`);
                          } else {
                            alert('No valid cards found in the CSV file.');
                          }
                        },
                        error: (error) => {
                          alert('Error parsing CSV file: ' + error.message);
                        }
                      });
                    }
                  };
                  input.click();
                }}
                title="Import CSV"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Upload className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => alert('Settings functionality coming soon!')}
                title="Settings"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Settings className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => setDarkMode(prev => !prev)}
                title="Toggle Dark Mode"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                {darkMode ? (
                  <Sun className="w-6 h-6 text-yellow-500 group-hover:scale-110 transition-transform" />
                ) : (
                  <Moon className="w-6 h-6 text-violet-600 group-hover:scale-110 transition-transform" />
                )}
              </button>
            </div>
          </div>
          <div className="flex items-center gap-3 bg-slate-50/50 dark:bg-slate-700/50 p-3 rounded-xl">
            <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg">
              <Filter className="w-4 h-4 text-white" />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="flex-1 px-4 py-2 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-violet-500 text-sm font-medium"
            >
              {getCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <div className="px-3 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-xl text-sm font-bold shadow-md">
              {studyDeck.length > 0 ? `${currentCardIndex + 1} / ${studyDeck.length}` : 'All Clear! 🎉'}
            </div>
          </div>
        </div>
      </header>

      <main className="w-full max-w-4xl h-[60vh] md:h-[65vh] relative flex-grow flex items-center justify-center z-0">
        {/* Navigation Buttons */}
        {studyDeck.length > 1 && currentDisplayCard && (
          <>
            <button
              onClick={goToPreviousCard}
              disabled={currentCardIndex === 0}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 p-3 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-110 disabled:hover:scale-100"
              title="Previous Card (←)"
            >
              <ChevronLeft className="w-6 h-6 text-violet-600 dark:text-violet-400" />
            </button>
            <button
              onClick={goToNextCard}
              disabled={currentCardIndex === studyDeck.length - 1}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 p-3 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-110 disabled:hover:scale-100"
              title="Next Card (→)"
            >
              <ChevronRight className="w-6 h-6 text-violet-600 dark:text-violet-400" />
            </button>
          </>
        )}
        {studyDeck.length === 0 && (
          <div className="text-center bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/30 dark:to-green-950/30 p-8 rounded-3xl border border-emerald-200/50 dark:border-emerald-800/30 shadow-xl">
            <div className="p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
              <CheckCircle size={48} className="text-white" />
            </div>
            <h2 className="text-2xl font-bold text-emerald-800 dark:text-emerald-200 mb-2">🎉 All Clear!</h2>
            <p className="text-emerald-600 dark:text-emerald-400 text-lg">No cards due in the selected category.</p>
            <p className="text-emerald-500 dark:text-emerald-500 text-sm mt-2">Great job staying on top of your studies!</p>
          </div>
        )}
        {currentDisplayCard && (
          <div className="absolute w-full h-full bg-gradient-to-br from-white via-slate-50/50 to-white dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 shadow-2xl rounded-3xl p-6 flex flex-col justify-between border border-violet-200/50 dark:border-purple-700/50 select-none backdrop-blur-sm">
            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex justify-between items-start mb-4">
                <span className="text-xs px-3 py-1.5 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full font-semibold shadow-md">{currentDisplayCard.category}</span>
                <span className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100/50 dark:bg-slate-700/50 px-2 py-1 rounded-lg">
                  S:{currentDisplayCard.stability?.toFixed(1)} D:{currentDisplayCard.difficulty?.toFixed(1)} R:{currentDisplayCard.reviewCount}
                </span>
              </div>
              <div className={`text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none whitespace-pre-wrap break-words leading-relaxed ${showAnswer ? 'pb-4 border-b border-slate-200 dark:border-slate-700' : ''}`}>
                {currentDisplayCard.question}
              </div>

              {showAnswer && (
                <div className="flex-1 flex flex-col min-h-0 mt-4">
                  <div className="flex-1 overflow-y-auto border border-violet-200/50 dark:border-purple-700/50 rounded-xl bg-slate-50/50 dark:bg-slate-800/50 p-1">
                    <JavaCodeHighlighter code={currentDisplayCard.answer} />
                  </div>
                </div>
              )}
            </div>

            {!showAnswer && (
              <div className="flex flex-col gap-2 mt-auto">
                <button
                  onClick={() => setShowAnswer(true)}
                  className="w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white py-4 rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 font-bold text-lg shadow-xl hover:shadow-2xl transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 dark:ring-offset-slate-900"
                  onKeyDown={(e) => { if (e.key === ' ' || e.key === 'Enter') { e.preventDefault(); setShowAnswer(true); } }}
                >
                  ✨ Show Answer <span className="ml-2 text-xs text-purple-200 bg-white/20 px-2 py-1 rounded-full">[Space]</span>
                </button>
              </div>
            )}

            {showAnswer && (
              <div className="flex flex-col gap-2 mt-4">
                <div className="grid grid-cols-2 gap-3">
                  <button
                    className="bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center gap-2"
                    onClick={() => handleGrade(currentDisplayCard.id, 1)}
                  >
                    <span>Again</span>
                    <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[1]</span>
                  </button>
                  <button
                    className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center gap-2"
                    onClick={() => handleGrade(currentDisplayCard.id, 2)}
                  >
                    <span>Hard</span>
                    <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[2]</span>
                  </button>
                  <button
                    className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center gap-2"
                    onClick={() => handleGrade(currentDisplayCard.id, 3)}
                  >
                    <span>Good</span>
                    <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[3]</span>
                  </button>
                  <button
                    className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center gap-2"
                    onClick={() => handleGrade(currentDisplayCard.id, 4)}
                  >
                    <span>Easy</span>
                    <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[4]</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}

export default App;