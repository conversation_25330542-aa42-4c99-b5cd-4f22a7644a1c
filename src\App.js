import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON>, <PERSON><PERSON><PERSON>cle, Filter, Zap, Sparkles, Upload, Settings, Plus, <PERSON>, Sun
} from 'lucide-react';
import <PERSON> from 'papa<PERSON><PERSON>';
import './index.css';

// --- FSRS Implementation ---
class FSRS {
  schedule(card, grade, now = new Date()) {
    const newCard = { ...card };
    newCard.lastReview = now.toISOString();
    newCard.reviewCount = (newCard.reviewCount || 0) + 1;

    switch (grade) {
      case 1: // Again
        newCard.difficulty = Math.min(10, newCard.difficulty + 0.8);
        newCard.stability = Math.max(0.1, newCard.stability * 0.8);
        newCard.dueDate = new Date(now.getTime() + 60000).toISOString(); // 1 minute
        break;
      case 2: // Hard
        newCard.difficulty = Math.min(10, newCard.difficulty + 0.15);
        newCard.stability = newCard.stability * 1.2;
        newCard.dueDate = new Date(now.getTime() + 86400000).toISOString(); // 1 day
        break;
      case 3: // Good
        newCard.difficulty = Math.max(1, newCard.difficulty - 0.1);
        newCard.stability = newCard.stability * 2.5;
        newCard.dueDate = new Date(now.getTime() + 259200000).toISOString(); // 3 days
        break;
      case 4: // Easy
        newCard.difficulty = Math.max(1, newCard.difficulty - 0.2);
        newCard.stability = newCard.stability * 3.0;
        newCard.dueDate = new Date(now.getTime() + 604800000).toISOString(); // 7 days
        break;
      default:
        break;
    }

    newCard.state = 'review';
    return newCard;
  }
}

// --- Initial Cards Data ---
const initialCardsData = [
  {
    id: 1,
    question: "What is a class in Java?",
    answer: "🔹 **Key Features:**\n\n1. A blueprint or template for creating objects\n2. Encapsulates data (fields) and methods\n3. Serves as a template for object creation\n4. Supports inheritance and polymorphism\n\n💡 **Example:**\n\nclass Car {\n    String brand;\n    void start() {\n        System.out.println(\"Car started\");\n    }\n}",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 2,
    question: "What is the difference between == and .equals() in Java?",
    answer: "🔹 **Key Differences:**\n\n1. == compares references (memory addresses)\n2. .equals() compares actual content\n3. For primitives, == compares values directly\n4. For objects, == compares memory addresses\n\n💡 **Example:**\n\nString a = new String(\"hello\");\nString b = new String(\"hello\");\nSystem.out.println(a == b);        // false\nSystem.out.println(a.equals(b));   // true",
    category: "Basic Concepts",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  }
];

function App() {
  const [cards, setCards] = useState(() => {
    const saved = localStorage.getItem('flashcards');
    return saved ? JSON.parse(saved) : initialCardsData;
  });
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [fsrs] = useState(() => new FSRS());
  const [darkMode, setDarkMode] = useState(false);

  // Save cards to localStorage whenever cards change
  useEffect(() => {
    localStorage.setItem('flashcards', JSON.stringify(cards));
  }, [cards]);

  // Get unique categories
  const getCategories = useMemo(() => {
    const categories = ['All', ...new Set(cards.map(card => card.category))];
    return categories;
  }, [cards]);

  // Filter cards based on selected category and due date
  const studyDeck = useMemo(() => {
    const now = new Date();
    return cards.filter(card => {
      const categoryMatch = selectedCategory === 'All' || card.category === selectedCategory;
      const isDue = !card.dueDate || new Date(card.dueDate) <= now;
      return categoryMatch && isDue;
    });
  }, [cards, selectedCategory]);

  // Reset card index when study deck changes
  useEffect(() => {
    setCurrentCardIndex(0);
  }, [studyDeck.length]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const handleGrade = useCallback((cardId, grade) => {
    const now = new Date();
    setCards(prevCards =>
      prevCards.map(card =>
        card.id === cardId ? fsrs.schedule(card, grade, now) : card
      )
    );
    setCurrentCardIndex(prev => prev + 1);
  }, [fsrs]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-sky-50 to-emerald-50 dark:from-slate-900 dark:via-purple-900/20 dark:to-slate-900 text-gray-800 dark:text-slate-200 flex flex-col items-center justify-center p-4 transition-all duration-500">
      <header className="w-full max-w-4xl mb-8 z-10">
        <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-violet-200/50 dark:border-purple-700/50">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl shadow-lg">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">JavaBrain Boost</h1>
                <p className="text-sm text-slate-500 dark:text-slate-400">Master Java with spaced repetition</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => alert('Add card functionality coming soon!')}
                title="Add New Card"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Plus className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.csv';
                  input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                      Papa.parse(file, {
                        header: true,
                        complete: (results) => {
                          const newCards = results.data
                            .filter(row => row.question && row.answer)
                            .map((row, index) => ({
                              id: Math.max(...cards.map(c => c.id), 0) + index + 1,
                              question: row.question || '',
                              answer: row.answer || '',
                              category: row.category || 'Imported',
                              difficulty: 5,
                              stability: 2.5,
                              state: 'new',
                              reviewCount: 0,
                              lastReview: null,
                              dueDate: null
                            }));

                          if (newCards.length > 0) {
                            setCards(prevCards => [...prevCards, ...newCards]);
                            alert(`Successfully imported ${newCards.length} cards!`);
                          } else {
                            alert('No valid cards found in the CSV file.');
                          }
                        },
                        error: (error) => {
                          alert('Error parsing CSV file: ' + error.message);
                        }
                      });
                    }
                  };
                  input.click();
                }}
                title="Import CSV"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Upload className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => alert('Settings functionality coming soon!')}
                title="Settings"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                <Settings className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
              </button>

              <button
                onClick={() => setDarkMode(prev => !prev)}
                title="Toggle Dark Mode"
                className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
              >
                {darkMode ? (
                  <Sun className="w-6 h-6 text-yellow-500 group-hover:scale-110 transition-transform" />
                ) : (
                  <Moon className="w-6 h-6 text-violet-600 group-hover:scale-110 transition-transform" />
                )}
              </button>
            </div>
          </div>
          <div className="flex items-center gap-3 bg-slate-50/50 dark:bg-slate-700/50 p-3 rounded-xl">
            <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg">
              <Filter className="w-4 h-4 text-white" />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="flex-1 px-4 py-2 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-violet-500 text-sm font-medium"
            >
              {getCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <div className="px-3 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-xl text-sm font-bold shadow-md">
              {studyDeck.length > 0 ? `${currentCardIndex + 1} / ${studyDeck.length}` : 'All Clear! 🎉'}
            </div>
          </div>
        </div>
      </header>

      <main className="w-full max-w-2xl h-[60vh] md:h-[65vh] relative flex-grow flex items-center justify-center z-0">
        {studyDeck.length === 0 && (
          <div className="text-center bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/30 dark:to-green-950/30 p-8 rounded-3xl border border-emerald-200/50 dark:border-emerald-800/30 shadow-xl">
            <div className="p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
              <CheckCircle size={48} className="text-white" />
            </div>
            <h2 className="text-2xl font-bold text-emerald-800 dark:text-emerald-200 mb-2">🎉 All Clear!</h2>
            <p className="text-emerald-600 dark:text-emerald-400 text-lg">No cards due in the selected category.</p>
            <p className="text-emerald-500 dark:text-emerald-500 text-sm mt-2">Great job staying on top of your studies!</p>
          </div>
        )}
        {currentDisplayCard && (
          <div className="absolute w-full h-full bg-gradient-to-br from-white via-slate-50/50 to-white dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 shadow-2xl rounded-3xl p-6 flex flex-col justify-between border border-violet-200/50 dark:border-purple-700/50 select-none backdrop-blur-sm">
            <div>
              <div className="flex justify-between items-start mb-4">
                <span className="text-xs px-3 py-1.5 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full font-semibold shadow-md">{currentDisplayCard.category}</span>
                <span className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100/50 dark:bg-slate-700/50 px-2 py-1 rounded-lg">
                  S:{currentDisplayCard.stability?.toFixed(1)} D:{currentDisplayCard.difficulty?.toFixed(1)} R:{currentDisplayCard.reviewCount}
                </span>
              </div>
              <div className="text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none whitespace-pre-wrap break-words leading-relaxed">
                {currentDisplayCard.question}
              </div>
            </div>

            <div className="flex flex-col gap-2 mt-auto">
              <button
                onClick={() => alert('Show answer functionality coming soon!')}
                className="w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white py-4 rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 font-bold text-lg shadow-xl hover:shadow-2xl transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 dark:ring-offset-slate-900"
              >
                ✨ Show Answer <span className="ml-2 text-xs text-purple-200 bg-white/20 px-2 py-1 rounded-full">[Space]</span>
              </button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;