question,answer,category
"What is a class in Java?","A blueprint or template for creating objects that defines attributes and methods","OOP"
"What is inheritance?","A mechanism where a new class acquires properties and methods of an existing class","OOP"
"What is the difference between == and .equals()?","== compares references, .equals() compares content/values","Basic Concepts"
"What is a constructor?","A special method used to initialize objects when they are created","OOP"
"What is the static keyword?","Belongs to the class rather than any instance, shared among all objects","Keywords"
"What is polymorphism?","The ability of objects to take multiple forms, achieved through method overriding and overloading","OOP"
"What is encapsulation?","The bundling of data and methods that operate on that data within a single unit","OOP"
"What is an interface?","A contract that defines what methods a class must implement, supports multiple inheritance","OOP"
"What is the difference between ArrayList and LinkedList?","ArrayList uses dynamic arrays (fast random access), LinkedList uses doubly-linked lists (fast insertion/deletion)","Collections"
"What is a HashMap?","A data structure that stores key-value pairs using hash table implementation","Collections"
"What is the final keyword?","Prevents inheritance (classes), overriding (methods), or reassignment (variables)","Keywords"
"What is exception handling?","A mechanism to handle runtime errors using try-catch-finally blocks","Advanced"
"What is the difference between checked and unchecked exceptions?","Checked exceptions must be handled at compile time, unchecked exceptions occur at runtime","Advanced"
"What is a thread?","A lightweight subprocess that allows concurrent execution of multiple parts of a program","Advanced"
"What is synchronization?","A mechanism to control access to shared resources in multithreaded environments","Advanced"
