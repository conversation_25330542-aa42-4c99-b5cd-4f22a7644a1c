import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Brain, RotateCcw, Plus, TrendingUp, BookOpen, Clock, Target, Upload,
  FileText, AlertCircle, CheckCircle, Code, ChevronDown, ChevronUp,
  ChevronLeft, ChevronRight, Filter, Settings, X, CalendarDays, Zap,
  ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown
} from 'lucide-react';
import Papa from 'papaparse';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css'; // Default styling for react-calendar
import { useDrag } from 'react-use-gesture';
import { animated, useSpring, to } from '@react-spring/web';
import { format, parseISO, startOfDay, isSameDay, addDays } from 'date-fns';

// Simple Code Display Component (No changes)
const JavaCodeHighlighter = ({ code, className = "" }) => (
  <pre className={`bg-gray-800 text-gray-200 p-4 rounded-lg overflow-x-auto text-sm font-mono ${className}`}>
    <code>{code || ''}</code>
  </pre>
);

// FSRS Algorithm Implementation (No changes to core logic)
class FSRS {
  constructor() {
    this.w = [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61]; // Default weights
  }
  initStability(grade) { return Math.max(this.w[grade - 1], 0.1); }
  initDifficulty(grade) { return Math.min(Math.max(this.w[4] - this.w[5] * (grade - 3), 1), 10); }
  nextInterval(stability) {
    const requestRetention = 0.9;
    return Math.max(1, Math.round(stability * Math.log(requestRetention) / Math.log(0.5)));
  }
  nextStability(difficulty, stability, retrievability, grade) {
    const hardPenalty = grade === 2 ? this.w[15] : 1;
    const easyBonus = grade === 4 ? this.w[16] : 1;
    if (grade === 1) {
      return this.w[11] * Math.pow(difficulty, -this.w[12]) * (Math.pow(stability + 1, this.w[13]) - 1) * Math.exp((1 - retrievability) * this.w[14]);
    } else {
      return stability * (1 + Math.exp(this.w[8]) * (11 - difficulty) * Math.pow(stability, -this.w[9]) * (Math.exp((1 - retrievability) * this.w[10]) - 1)) * hardPenalty * easyBonus;
    }
  }
  nextDifficulty(difficulty, grade) {
    const newDifficulty = difficulty - this.w[6] * (grade - 3);
    return Math.min(Math.max(newDifficulty, 1), 10);
  }
  forgettingCurve(elapsedDays, stability) { return Math.pow(1 + elapsedDays / (9 * stability), -1); }
  schedule(card, grade, reviewDate = new Date()) {
    const elapsedDays = card.lastReview ? (reviewDate.getTime() - new Date(card.lastReview).getTime()) / (1000 * 60 * 60 * 24) : 0;
    let { difficulty, stability } = card;
    if (card.state === 'new') {
      difficulty = this.initDifficulty(grade);
      stability = this.initStability(grade);
    } else {
      const retrievability = this.forgettingCurve(elapsedDays, stability);
      difficulty = this.nextDifficulty(difficulty, grade);
      stability = this.nextStability(difficulty, stability, retrievability, grade);
    }
    const interval = this.nextInterval(stability);
    const dueDate = addDays(reviewDate, interval); // Use addDays for robust date addition
    return {
      ...card,
      difficulty: Math.round(difficulty * 100) / 100,
      stability: Math.round(stability * 100) / 100,
      interval,
      dueDate: format(dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"), // Store as ISO string
      lastReview: format(reviewDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      reviewCount: (card.reviewCount || 0) + 1,
      state: grade === 1 ? 'learning' : (grade === 2 ? 'relearning' : 'review')
    };
  }
}

const initialCards = [ // Ensure dueDates are properly formatted if manually setting
  { id: 1, question: "What is `static` in Java?", answer: "Belongs to the class, not instances.", category: "Keywords", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
  { id: 2, question: "Java `ArrayList` vs `LinkedList`?", answer: "ArrayList: dynamic array, fast random access. LinkedList: doubly-linked list, fast insertion/deletion.", category: "Collections", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
];

const AnimatedCard = ({ card, onSwipe, isTopCard, style }) => {
  if (!card) return null;

  const [showAnswer, setShowAnswer] = useState(false);
  const [{ x, y, rotateZ, scale, opacity }, api] = useSpring(() => ({
    x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1,
    config: { tension: 300, friction: 30 }
  }));

  useEffect(() => { // Reset answer visibility when card changes
      setShowAnswer(false);
      api.start({ x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1, immediate: true });
  }, [card, api]);


  const bind = useDrag(({ active, movement: [mx, my], direction: [xDir, yDir], velocity: [vx, vy], down, Canceled }) => {
    if (!isTopCard) return;

    const triggerDistance = window.innerWidth / 3.5; // How far to swipe to trigger action
    const isGone = Math.abs(mx) > triggerDistance || Math.abs(my) > triggerDistance;

    if (!active && isGone) {
      let grade = 0;
      // Determine grade based on swipe direction
      if (Math.abs(mx) > Math.abs(my)) { // Horizontal swipe
        grade = mx > 0 ? 3 : 1; // Right: Good (3), Left: Again (1)
      } else { // Vertical swipe
        grade = my > 0 ? 2 : 4; // Down: Hard (2), Up: Easy (4)
      }
      onSwipe(card.id, grade);
      return; // Card is gone, don't reset
    }

    api.start({
      x: active ? mx : 0,
      y: active ? my : 0,
      rotateZ: active ? mx / 20 : 0,
      scale: active ? 1.05 : 1,
      opacity: active ? (isGone ? 0 : 1) : 1,
      immediate: down,
    });
  });

  const getSwipeIndicator = () => {
    const currentX = x.get();
    const currentY = y.get();
    if (Math.abs(currentX) > 30 || Math.abs(currentY) > 30) {
      if (Math.abs(currentX) > Math.abs(currentY)) {
        return currentX > 0 ? { text: "Good", icon: <ThumbsUp />, color: "text-emerald-500", arrow: <ArrowRight/> } : { text: "Again", icon: <ThumbsDown />, color: "text-red-500", arrow: <ArrowLeft/> };
      } else {
        return currentY > 0 ? { text: "Hard", icon: <Meh />, color: "text-amber-500", arrow: <ArrowDown/> } : { text: "Easy", icon: <Sparkles />, color: "text-sky-500", arrow: <ArrowUp/> };
      }
    }
    return null;
  };
  const swipeIndicator = getSwipeIndicator();

  return (
    <animated.div
      {...(isTopCard ? bind() : {})}
      style={{
        ...style,
        x, y, rotateZ, scale, opacity,
        touchAction: isTopCard ? 'none' : 'auto', // Important for mobile
      }}
      className={`absolute w-full h-full cursor-grab active:cursor-grabbing bg-white dark:bg-slate-800 shadow-2xl rounded-2xl p-6 flex flex-col justify-between border border-slate-200 dark:border-slate-700 select-none`}
    >
      {swipeIndicator && isTopCard && (
        <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-opacity duration-200 p-4 rounded-lg flex flex-col items-center gap-1 ${swipeIndicator.color} bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm shadow-lg text-2xl font-bold z-10`}>
          {React.cloneElement(swipeIndicator.icon, { size: 36, className: "mb-1" })}
          <span>{swipeIndicator.text}</span>
          {React.cloneElement(swipeIndicator.arrow, { size: 20, className: "mt-1 opacity-70" })}
        </div>
      )}
      <div className={`transition-opacity duration-300 ${swipeIndicator && isTopCard ? 'opacity-20' : 'opacity-100'}`}>
        <div className="flex justify-between items-start mb-3">
          <span className="text-xs px-3 py-1 bg-indigo-100 dark:bg-indigo-700/30 text-indigo-700 dark:text-indigo-300 rounded-full font-semibold">{card.category}</span>
          <span className="text-xs text-slate-400 dark:text-slate-500">
            S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
          </span>
        </div>
        <div className={`text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none ${showAnswer ? 'pb-4 border-b border-slate-200 dark:border-slate-700' : ''}`}>
          {card.question}
        </div>
        {showAnswer && (
          <div className="text-slate-600 dark:text-slate-300 mt-4 prose-sm md:prose max-w-none overflow-y-auto max-h-48">
            <JavaCodeHighlighter code={card.answer} />
          </div>
        )}
      </div>
      {!showAnswer && (
        <button
          onClick={() => setShowAnswer(true)}
          className="mt-auto w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold text-lg shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:ring-offset-slate-900"
        >
          Show Answer
        </button>
      )}
      {showAnswer && (
         <div className="mt-auto text-center text-sm text-slate-500">Swipe to rate or use keyboard (1-4)</div>
      )}
    </animated.div>
  );
};


export default function JavaFlashcardApp() {
  const [cards, setCards] = useState(initialCards);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [fsrs] = useState(new FSRS());
  const [showSettings, setShowSettings] = useState(false); // For settings modal
  const [showAddCard, setShowAddCard] = useState(false); // For add card modal
  const [newCardData, setNewCardData] = useState({ question: '', answer: '', category: 'Basic Concepts' });
  const [uploadStatus, setUploadStatus] = useState(null);
  const fileInputRef = useRef(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      setDarkMode(localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && prefersDark));
    }
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);


  const getDueCards = useMemo(() => {
    const now = startOfDay(new Date());
    return cards.filter(card =>
      card.state === 'new' || (card.dueDate && startOfDay(parseISO(card.dueDate)) <= now)
    ).sort((a,b) => (a.dueDate && b.dueDate) ? parseISO(a.dueDate) - parseISO(b.dueDate) : (a.dueDate ? -1 : 1)); // Sort new cards or by due date
  }, [cards]);

  const studyDeck = useMemo(() => {
    const dueInSelectedCategory = getDueCards.filter(card =>
      selectedCategory === 'All Categories' || card.category === selectedCategory
    );
    return dueInSelectedCategory;
  }, [getDueCards, selectedCategory]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const getCategories = useMemo(() => {
    const uniqueCategories = [...new Set(cards.map(card => card.category).filter(Boolean))];
    return ['All Categories', ...uniqueCategories.sort()];
  }, [cards]);

  const dueCountsByDate = useMemo(() => {
    const counts = {};
    cards.forEach(card => {
      if (card.dueDate) {
        const dateStr = format(startOfDay(parseISO(card.dueDate)), 'yyyy-MM-dd');
        counts[dateStr] = (counts[dateStr] || 0) + 1;
      }
    });
    return counts;
  }, [cards]);

  const handleGrade = (cardId, grade) => {
    const cardToGrade = cards.find(c => c.id === cardId);
    if (!cardToGrade) return;

    const updatedCard = fsrs.schedule(cardToGrade, grade);
    const updatedCards = cards.map(c => (c.id === cardId ? updatedCard : c));
    setCards(updatedCards);

    // Advance to next card in studyDeck
    if (currentCardIndex < studyDeck.length - 1) {
      setCurrentCardIndex(prev => prev + 1);
    } else { // Last card in deck or deck becomes empty
      setCurrentCardIndex(0); // Loop or show completion
    }
  };

  // Swipe handler
  const handleSwipe = (cardId, grade) => {
    handleGrade(cardId, grade);
  };

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showSettings || showAddCard || showCalendar || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
      if (!currentDisplayCard) return;

      switch (e.key) {
        case '1': handleGrade(currentDisplayCard.id, 1); break;
        case '2': handleGrade(currentDisplayCard.id, 2); break;
        case '3': handleGrade(currentDisplayCard.id, 3); break;
        case '4': handleGrade(currentDisplayCard.id, 4); break;
        // Add other shortcuts if needed, like space to flip (handled in AnimatedCard now)
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentDisplayCard, showSettings, showAddCard, showCalendar, handleGrade]); // Ensure handleGrade is stable or memoized if complex

  const addNewCardToList = () => { /* ... (add card logic, similar to previous version) ... */ };
  const handleFileUpload = async (event) => { /* ... (file upload logic, similar to previous version) ... */ };

  // Simplified controls - primary interaction is swipe
  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''} bg-slate-100 dark:bg-slate-900 text-slate-800 dark:text-slate-200 transition-colors duration-300 flex flex-col items-center p-4 relative overflow-hidden`}>
      {/* Header & Controls */}
      <header className="w-full max-w-3xl mb-6 z-10">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
            <h1 className="text-2xl font-bold">JavaBrain Boost</h1>
          </div>
          <div className="flex items-center gap-3">
            <button onClick={() => setShowCalendar(true)} title="Due Dates Calendar" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
              <CalendarDays className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </button>
            <button onClick={() => setDarkMode(!darkMode)} title="Toggle Dark Mode" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
              {darkMode ? <Sparkles className="w-6 h-6 text-yellow-400" /> : <Zap className="w-6 h-6 text-slate-600" />}
            </button>
            {/* Add Settings/Add Card buttons if needed */}
          </div>
        </div>
        <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-slate-500" />
            <select
              value={selectedCategory}
              onChange={(e) => {setSelectedCategory(e.target.value); setCurrentCardIndex(0);}}
              className="px-3 py-1.5 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
            >
              {getCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <span className="text-sm text-slate-500 dark:text-slate-400 ml-auto">
              {studyDeck.length > 0 ? `${currentCardIndex + 1} / ${studyDeck.length}` : 'No cards due'}
            </span>
        </div>
      </header>

      {/* Card Stack Area */}
      <main className="w-full max-w-md h-[65vh] md:h-[70vh] relative flex-grow flex items-center justify-center z-0">
        {studyDeck.length === 0 && (
          <div className="text-center text-slate-500 dark:text-slate-400">
            <CheckCircle size={48} className="mx-auto mb-3 text-green-500" />
            <p className="text-xl font-semibold">All clear for now!</p>
            <p>No cards due in "{selectedCategory}".</p>
          </div>
        )}
        {/* Render only a few cards for performance, top card interactive */}
        {studyDeck.slice(currentCardIndex, currentCardIndex + 3).reverse().map((card, indexInSlice) => {
            const isTopCard = indexInSlice === (studyDeck.slice(currentCardIndex, currentCardIndex + 3).length - 1);
            const springStyle = { // Stacking effect
                transform: `translateY(${-indexInSlice * 10}px) scale(${1 - indexInSlice * 0.05})`,
                zIndex: studyDeck.length - indexInSlice,
            };
            return (
                <AnimatedCard
                    key={card.id}
                    card={card}
                    onSwipe={handleSwipe}
                    isTopCard={isTopCard}
                    style={springStyle}
                />
            );
        })}
      </main>

      {/* Swipe Instruction Footer */}
      {currentDisplayCard && (
        <footer className="w-full max-w-md mt-auto text-center py-3 text-xs text-slate-500 dark:text-slate-400 z-10">
          Swipe: <ArrowLeft className="inline h-3 w-3"/> Again | <ArrowDown className="inline h-3 w-3"/> Hard | <ArrowRight className="inline h-3 w-3"/> Good | <ArrowUp className="inline h-3 w-3"/> Easy. Or use keys 1-4.
        </footer>
      )}


      {/* Calendar Modal */}
      {showCalendar && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" onClick={() => setShowCalendar(false)}>
          <div className="bg-white dark:bg-slate-800 p-4 rounded-xl shadow-2xl max-w-sm w-full" onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center mb-3">
                <h3 className="text-lg font-semibold text-indigo-700 dark:text-indigo-300">Due Dates Calendar</h3>
                <button onClick={() => setShowCalendar(false)} className="p-1 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700"><X size={20}/></button>
            </div>
            <Calendar
              className="!border-none custom-calendar" // Custom class for further styling
              tileContent={({ date, view }) => {
                if (view === 'month') {
                  const dateStr = format(date, 'yyyy-MM-dd');
                  const count = dueCountsByDate[dateStr];
                  if (count > 0) {
                    return <span className="absolute bottom-1 right-1 text-xs bg-indigo-500 text-white rounded-full w-4 h-4 flex items-center justify-center">{count}</span>;
                  }
                }
                return null;
              }}
              // You can add onActiveStartDateChange or onClickDay if needed
            />
          </div>
        </div>
      )}

      {/* Add Global Styles for Calendar if needed */}
      <style jsx global>{`
        .custom-calendar .react-calendar__tile--now {
          background: #e0e7ff !important; /* Indigo-100 for today */
          color: #4f46e5 !important; /* Indigo-700 */
        }
        .dark .custom-calendar .react-calendar__tile--now {
          background: #3730a3 !important; /* Indigo-800 for dark mode today */
          color: #a5b4fc !important; /* Indigo-300 */
        }
        .custom-calendar .react-calendar__tile:enabled:hover,
        .custom-calendar .react-calendar__tile:enabled:focus {
          background-color: #c7d2fe; /* Indigo-200 */
        }
        .dark .custom-calendar .react-calendar__tile:enabled:hover,
        .dark .custom-calendar .react-calendar__tile:enabled:focus {
          background-color: #4338ca; /* Indigo-700 */
        }
        .custom-calendar .react-calendar__navigation button {
          color: #4f46e5; /* Indigo-700 */
          font-weight: bold;
        }
        .dark .custom-calendar .react-calendar__navigation button {
          color: #a5b4fc; /* Indigo-300 */
        }
        .custom-calendar .react-calendar__month-view__days__day--weekend {
            color: #ef4444; /* Red-500 for weekends, example */
        }
        .dark .custom-calendar .react-calendar__month-view__days__day--weekend {
            color: #f87171; /* Red-400 for weekends dark */
        }
        .custom-calendar abbr[title] { /* Remove underline from day numbers */
            text-decoration: none;
        }
        .react-calendar__tile {
            position: relative; /* For badge positioning */
        }
      `}</style>
    </div>
  );
}