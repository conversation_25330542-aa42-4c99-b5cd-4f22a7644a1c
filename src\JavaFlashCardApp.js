import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON>, CheckCircle, Filter, X, CalendarDays, Zap,
  ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown
} from 'lucide-react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css'; // Default styling for react-calendar
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, parseISO, startOfDay, addDays } from 'date-fns';

// --- utils/fsrs.js ---
// FSRS Algorithm Implementation (No changes to core logic, conceptually moved)
class FSRS {
  constructor() {
    this.w = [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61]; // Default weights
  }
  initStability(grade) { return Math.max(this.w[grade - 1], 0.1); }
  initDifficulty(grade) { return Math.min(Math.max(this.w[4] - this.w[5] * (grade - 3), 1), 10); }
  nextInterval(stability) {
    const requestRetention = 0.9;
    return Math.max(1, Math.round(stability * Math.log(requestRetention) / Math.log(0.5)));
  }
  nextStability(difficulty, stability, retrievability, grade) {
    const hardPenalty = grade === 2 ? this.w[15] : 1;
    const easyBonus = grade === 4 ? this.w[16] : 1;
    if (grade === 1) {
      return this.w[11] * Math.pow(difficulty, -this.w[12]) * (Math.pow(stability + 1, this.w[13]) - 1) * Math.exp((1 - retrievability) * this.w[14]);
    } else {
      return stability * (1 + Math.exp(this.w[8]) * (11 - difficulty) * Math.pow(stability, -this.w[9]) * (Math.exp((1 - retrievability) * this.w[10]) - 1)) * hardPenalty * easyBonus;
    }
  }
  nextDifficulty(difficulty, grade) {
    const newDifficulty = difficulty - this.w[6] * (grade - 3);
    return Math.min(Math.max(newDifficulty, 1), 10);
  }
  forgettingCurve(elapsedDays, stability) { return Math.pow(1 + elapsedDays / (9 * stability), -1); }
  schedule(card, grade, reviewDate = new Date()) {
    const elapsedDays = card.lastReview ? (reviewDate.getTime() - new Date(card.lastReview).getTime()) / (1000 * 60 * 60 * 24) : 0;
    let { difficulty, stability } = card;
    if (card.state === 'new') {
      difficulty = this.initDifficulty(grade);
      stability = this.initStability(grade);
    } else {
      const retrievability = this.forgettingCurve(elapsedDays, stability);
      difficulty = this.nextDifficulty(difficulty, grade);
      stability = this.nextStability(difficulty, stability, retrievability, grade);
    }
    const interval = this.nextInterval(stability);
    const dueDate = addDays(reviewDate, interval);
    return {
      ...card,
      difficulty: Math.round(difficulty * 100) / 100,
      stability: Math.round(stability * 100) / 100,
      interval,
      dueDate: format(dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      lastReview: format(reviewDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      reviewCount: (card.reviewCount || 0) + 1,
      state: grade === 1 ? 'learning' : (grade === 2 ? 'relearning' : 'review')
    };
  }
}

// --- data/initialCards.js ---
// (Conceptually moved)
const initialCardsData = [
  { id: 1, question: "What is `static` in Java?", answer: "Belongs to the class, not instances.", category: "Keywords", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
  { id: 2, question: "Java `ArrayList` vs `LinkedList`?", answer: "ArrayList: dynamic array, fast random access. LinkedList: doubly-linked list, fast insertion/deletion.", category: "Collections", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
];

// --- components/JavaCodeHighlighter.jsx ---
// (Conceptually moved, no changes to component itself)
const JavaCodeHighlighter = ({ code, className = "" }) => (
  <pre className={`bg-gray-800 text-gray-200 p-4 rounded-lg overflow-x-auto text-sm font-mono ${className}`}>
    <code>{code || ''}</code>
  </pre>
);

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }, // Changed from blue to sky to match icon
];

// --- components/AnimatedCard.jsx ---
// (Conceptually moved and refactored)
const AnimatedCard = ({ card, onSwipe, isTopCard, style }) => {
  const [showAnswer, setShowAnswer] = useState(false);
  const [{ x, y, rotateZ, scale, opacity }, api] = useSpring(() => ({
    x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1,
    config: { tension: 300, friction: 30 }
  }));

  useEffect(() => {
    setShowAnswer(false);
    api.start({ x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1, immediate: true });
  }, [card, api]);

  const bind = useDrag(({ active, movement: [mx, my], down }) => {
    if (!isTopCard) return;

    const triggerDistance = window.innerWidth / 3.5;
    const isGone = Math.abs(mx) > triggerDistance || Math.abs(my) > triggerDistance;

    if (!active && isGone) {
      let grade = 0;
      if (Math.abs(mx) > Math.abs(my)) { // Horizontal swipe
        grade = mx > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good").grade : GRADING_OPTIONS.find(opt => opt.label === "Again").grade;
      } else { // Vertical swipe
        grade = my > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard").grade : GRADING_OPTIONS.find(opt => opt.label === "Easy").grade;
      }
      onSwipe(card.id, grade);
      return;
    }

    api.start({
      x: active ? mx : 0,
      y: active ? my : 0,
      rotateZ: active ? mx / 20 : 0,
      scale: active ? 1.05 : 1,
      opacity: active ? (isGone ? 0 : 1) : 1,
      immediate: down,
    });
  });

  const getSwipeIndicator = () => {
    if (!isTopCard || (!x.isAnimating && !y.isAnimating && x.get() === 0 && y.get() === 0)) return null;

    const currentX = x.get();
    const currentY = y.get();

    if (Math.abs(currentX) > 30 || Math.abs(currentY) > 30) {
      if (Math.abs(currentX) > Math.abs(currentY)) {
        return currentX > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good") : GRADING_OPTIONS.find(opt => opt.label === "Again");
      } else {
        return currentY > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard") : GRADING_OPTIONS.find(opt => opt.label === "Easy");
      }
    }
    return null;
  };

  const swipeIndicatorOption = getSwipeIndicator();

  if (!card) return null;

  return (
    <animated.div
      {...(isTopCard ? bind() : {})}
      style={{
        ...style,
        x, y, rotateZ, scale, opacity,
        touchAction: isTopCard ? 'none' : 'auto',
      }}
      className="absolute w-full h-full cursor-grab active:cursor-grabbing bg-white dark:bg-slate-800 shadow-2xl rounded-2xl p-6 flex flex-col justify-between border border-slate-200 dark:border-slate-700 select-none"
    >
      {swipeIndicatorOption && isTopCard && (
        <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-opacity duration-200 p-4 rounded-lg flex flex-col items-center gap-1 ${swipeIndicatorOption.feedbackColor} bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm shadow-lg text-2xl font-bold z-10`}>
          <swipeIndicatorOption.icon size={36} className="mb-1" />
          <span>{swipeIndicatorOption.label}</span>
          <swipeIndicatorOption.arrowIcon size={20} className="mt-1 opacity-70" />
        </div>
      )}
      <div className={`transition-opacity duration-300 ${swipeIndicatorOption && isTopCard ? 'opacity-20' : 'opacity-100'}`}>
        <div className="flex justify-between items-start mb-3">
          <span className="text-xs px-3 py-1 bg-indigo-100 dark:bg-indigo-700/30 text-indigo-700 dark:text-indigo-300 rounded-full font-semibold">{card.category}</span>
          <span className="text-xs text-slate-400 dark:text-slate-500">
            S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
          </span>
        </div>
        <div className={`text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none ${showAnswer ? 'pb-4 border-b border-slate-200 dark:border-slate-700' : ''}`}>
          {card.question}
        </div>
        {showAnswer && (
          <div className="text-slate-600 dark:text-slate-300 mt-4 prose-sm md:prose max-w-none overflow-y-auto max-h-48">
            <JavaCodeHighlighter code={card.answer} />
          </div>
        )}
      </div>

      {!showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <button
            onClick={() => setShowAnswer(true)}
            className="w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold text-lg shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:ring-offset-slate-900"
            aria-label="Show Answer (Space)"
            onKeyDown={e => { if (e.key === ' ' || e.key === 'Enter') { e.preventDefault(); setShowAnswer(true); } }}
          >
            Show Answer <span className="ml-2 text-xs text-indigo-200">[Space]</span>
          </button>
        </div>
      )}
      {showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <div className="grid grid-cols-2 gap-2">
            {GRADING_OPTIONS.map(opt => (
              <button
                key={opt.grade}
                className={`bg-${opt.color}-500 text-white py-2 rounded-lg font-semibold shadow hover:bg-${opt.color}-600 focus:outline-none focus:ring-2 focus:ring-${opt.color}-400`}
                onClick={() => onSwipe(card.id, opt.grade)}
                aria-label={`${opt.label} (${opt.shortcut})`}
              >
                <opt.arrowIcon className="inline h-4 w-4 mr-1.5" /> {opt.label} <span className="ml-1 text-xs">[{opt.shortcut}]</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </animated.div>
  );
};

// --- hooks/useDarkMode.js ---
// (Conceptually moved)
const useDarkMode = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      const storedTheme = localStorage.getItem('theme');
      setDarkMode(storedTheme === 'dark' || (!storedTheme && prefersDark));
    }
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);

  return [darkMode, setDarkMode];
};

// --- components/AppHeader.jsx ---
const AppHeader = ({ selectedCategory, onCategoryChange, categories, onToggleCalendar, onToggleDarkMode, darkMode, studyDeckLength, currentCardOrderInDeck }) => (
  <header className="w-full max-w-3xl mb-6 z-10">
    <div className="flex justify-between items-center mb-4">
      <div className="flex items-center gap-2">
        <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
        <h1 className="text-2xl font-bold">JavaBrain Boost</h1>
      </div>
      <div className="flex items-center gap-3">
        <button onClick={onToggleCalendar} title="Due Dates Calendar" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
          <CalendarDays className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
        </button>
        <button onClick={() => onToggleDarkMode(prev => !prev)} title="Toggle Dark Mode" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
          {darkMode ? <Sparkles className="w-6 h-6 text-yellow-400" /> : <Zap className="w-6 h-6 text-slate-600" />}
        </button>
        {/* Placeholder for Add/Settings buttons
        <button title="Add New Card" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700"><Plus size={24} /></button>
        <button title="Settings" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700"><Settings size={24} /></button>
        */}
      </div>
    </div>
    <div className="flex items-center gap-2">
      <Filter className="w-5 h-5 text-slate-500" />
      <select
        value={selectedCategory}
        onChange={onCategoryChange}
        className="px-3 py-1.5 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
      >
        {categories.map(category => (
          <option key={category} value={category}>{category}</option>
        ))}
      </select>
      <span className="text-sm text-slate-500 dark:text-slate-400 ml-auto">
        {studyDeckLength > 0 ? `${currentCardOrderInDeck} / ${studyDeckLength}` : 'No cards due'}
      </span>
    </div>
  </header>
);

// --- components/CardDisplayArea.jsx ---
const CardDisplayArea = ({ studyDeck, currentCardIndex, handleSwipe }) => (
  <main className="w-full max-w-md h-[60vh] md:h-[65vh] relative flex-grow flex items-center justify-center z-0">
    {studyDeck.length === 0 && (
      <div className="text-center text-slate-500 dark:text-slate-400">
        <CheckCircle size={48} className="mx-auto mb-3 text-green-500" />
        <p className="text-xl font-semibold">All clear for now!</p>
        <p>No cards due in the selected category.</p>
      </div>
    )}
    {/* Render only a few cards for performance, top card interactive */}
    {studyDeck.slice(currentCardIndex, currentCardIndex + 3).reverse().map((card, indexInSlice) => {
      const isTopCard = indexInSlice === (studyDeck.slice(currentCardIndex, currentCardIndex + 3).length - 1);
      const cardStyle = { // Stacking effect
        transform: `translateY(${-indexInSlice * 10}px) scale(${1 - indexInSlice * 0.05})`,
        zIndex: studyDeck.length - indexInSlice,
      };
      return (
        <AnimatedCard
          key={card.id}
          card={card}
          onSwipe={handleSwipe}
          isTopCard={isTopCard}
          style={cardStyle}
        />
      );
    })}
  </main>
);

// --- components/SwipeInstructionsSidebar.jsx ---
const SwipeInstructionsSidebar = ({ show }) => {
  if (!show) return null;
  return (
    <div className="hidden xl:block fixed right-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-slate-800 rounded-xl shadow-lg p-4 border border-slate-200 dark:border-slate-700 z-10 max-w-xs">
      <h3 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 text-center">📱 Swipe Directions</h3>
      <div className="space-y-3 text-sm">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300">
          <div className="flex items-center gap-1">
            <span>⬅️</span>
            <ArrowLeft className="h-4 w-4" />
          </div>
          <span className="font-medium">Left: Again</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300">
          <div className="flex items-center gap-1">
            <span>➡️</span>
            <ArrowRight className="h-4 w-4" />
          </div>
          <span className="font-medium">Right: Good</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300">
          <div className="flex items-center gap-1">
            <span>⬇️</span>
            <ArrowDown className="h-4 w-4" />
          </div>
          <span className="font-medium">Down: Hard</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-sky-50 dark:bg-sky-900/20 text-sky-700 dark:text-sky-300">
          <div className="flex items-center gap-1">
            <span>⬆️</span>
            <ArrowUp className="h-4 w-4" />
          </div>
          <span className="font-medium">Up: Easy</span>
        </div>
      </div>
      <div className="mt-4 pt-3 border-t border-slate-200 dark:border-slate-600 text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">⌨️ Or use keys 1-4</p>
      </div>
    </div>
  );
};

// --- components/SwipeInstructionFooter.jsx ---
const SwipeInstructionFooter = ({ show }) => {
  if (!show) return null;
  return (
    <footer className="w-full max-w-md mt-auto text-center py-3 text-xs text-slate-500 dark:text-slate-400 z-10 xl:hidden">
      <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
        <p className="mb-2 font-medium">📱 Swipe Directions:</p>
        <div className="flex flex-wrap justify-center gap-2 text-xs">
          <span className="text-red-600">⬅️ Again</span>
          <span className="text-amber-600">⬇️ Hard</span>
          <span className="text-emerald-600">➡️ Good</span>
          <span className="text-sky-600">⬆️ Easy</span>
        </div>
        <p className="mt-2 text-xs">⌨️ Or use keys 1-4</p>
      </div>
    </footer>
  );
};

// --- components/CalendarModal.jsx ---
const CalendarModal = ({ isOpen, onClose, dueCountsByDate }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-white dark:bg-slate-800 p-4 rounded-xl shadow-2xl max-w-sm w-full" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold text-indigo-700 dark:text-indigo-300">Due Dates Calendar</h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700"><X size={20}/></button>
        </div>
        <Calendar
          className="!border-none custom-calendar-theme" // Apply custom theme class
          tileContent={({ date, view }) => {
            if (view === 'month') {
              const dateStr = format(date, 'yyyy-MM-dd');
              const count = dueCountsByDate[dateStr];
              if (count > 0) {
                return <span className="absolute bottom-1 right-1 text-xs bg-indigo-500 text-white rounded-full w-4 h-4 flex items-center justify-center">{count}</span>;
              }
            }
            return null;
          }}
        />
      </div>
    </div>
  );
};


// --- JavaFlashcardApp.jsx (Main Component) ---
export default function JavaFlashcardApp() {
  const [cards, setCards] = useState(initialCardsData);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [fsrs] = useState(() => new FSRS()); // Initialize fsrs instance once
  const [showCalendar, setShowCalendar] = useState(false);
  const [darkMode, setDarkMode] = useDarkMode();

  const getDueCards = useMemo(() => {
    const now = startOfDay(new Date());
    return cards.filter(card =>
      card.state === 'new' || (card.dueDate && startOfDay(parseISO(card.dueDate)) <= now)
    ).sort((a,b) => (a.dueDate && b.dueDate) ? parseISO(a.dueDate) - parseISO(b.dueDate) : (a.dueDate ? -1 : 1));
  }, [cards]);

  const studyDeck = useMemo(() => {
    return getDueCards.filter(card =>
      selectedCategory === 'All Categories' || card.category === selectedCategory
    );
  }, [getDueCards, selectedCategory]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const getCategories = useMemo(() => {
    const uniqueCategories = [...new Set(cards.map(card => card.category).filter(Boolean))];
    return ['All Categories', ...uniqueCategories.sort()];
  }, [cards]);

  const dueCountsByDate = useMemo(() => {
    const counts = {};
    cards.forEach(card => {
      if (card.dueDate) {
        const dateStr = format(startOfDay(parseISO(card.dueDate)), 'yyyy-MM-dd');
        counts[dateStr] = (counts[dateStr] || 0) + 1;
      }
    });
    return counts;
  }, [cards]);

  const handleGrade = useCallback((cardId, grade) => {
    const cardToGrade = cards.find(c => c.id === cardId);
    if (!cardToGrade) return;

    const updatedCard = fsrs.schedule(cardToGrade, grade);
    setCards(prevCards => prevCards.map(c => (c.id === cardId ? updatedCard : c)));

    setCurrentCardIndex(prev => {
        // Check against the most up-to-date studyDeck length that will be recalculated
        // This logic might need slight adjustment if studyDeck recalculation is deferred
        // For now, assuming studyDeck updates quickly enough.
        // A more robust way might be to recalculate next index based on new studyDeck.
      if (prev < studyDeck.length -1) { // If there are more cards in the current filtered deck
        return prev + 1;
      }
      return 0; // Loop back or handle completion
    });

  }, [cards, fsrs, studyDeck.length]); // studyDeck.length is a dependency, ensure it's stable or causes intended recalculations


  useEffect(() => {
    // Reset currentCardIndex if studyDeck changes (e.g., due to category filter)
    // to avoid out-of-bounds errors.
    setCurrentCardIndex(0);
  }, [studyDeck.length, selectedCategory]); // Depends on selectedCategory as well as it affects studyDeck

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showCalendar || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;
      if (!currentDisplayCard) return;

      // Find the grading option based on key press
      const gradingOpt = GRADING_OPTIONS.find(opt => opt.shortcut === e.key);
      if (gradingOpt) {
        e.preventDefault(); // Prevent default browser actions for '1'-'4'
        handleGrade(currentDisplayCard.id, gradingOpt.grade);
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentDisplayCard, showCalendar, handleGrade]);

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    // setCurrentCardIndex(0); // Already handled by useEffect depending on studyDeck.length
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''} bg-slate-100 dark:bg-slate-900 text-slate-800 dark:text-slate-200 transition-colors duration-300 flex flex-col items-center p-4 relative overflow-hidden`}>
      <AppHeader
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        categories={getCategories}
        onToggleCalendar={() => setShowCalendar(true)}
        onToggleDarkMode={setDarkMode}
        darkMode={darkMode}
        studyDeckLength={studyDeck.length}
        currentCardOrderInDeck={studyDeck.length > 0 ? currentCardIndex + 1 : 0}
      />

      <CardDisplayArea
        studyDeck={studyDeck}
        currentCardIndex={currentCardIndex}
        handleSwipe={handleGrade} // handleGrade also serves as handleSwipe
      />

      <SwipeInstructionsSidebar show={!!currentDisplayCard} />
      <SwipeInstructionFooter show={!!currentDisplayCard} />

      <CalendarModal
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
        dueCountsByDate={dueCountsByDate}
      />

      {/* Placeholder for AddCardModal and SettingsModal */}
      {/* {showAddCard && <AddCardModal onClose={() => setShowAddCard(false)} onAdd={addNewCardToList} />} */}
      {/* {showSettings && <SettingsModal onClose={() => setShowSettings(false)} />} */}

      {/* Global Styles for react-calendar (scoped with a custom class) */}
      {/* It's generally better to put these in a global CSS file if possible,
          but for self-contained components this <style jsx global> pattern is common. */}
      <style jsx global>{`
        .custom-calendar-theme .react-calendar__tile--now {
          background: #e0e7ff !important; /* Tailwind Indigo-100 */
          color: #4f46e5 !important; /* Tailwind Indigo-700 */
          font-weight: bold;
        }
        .dark .custom-calendar-theme .react-calendar__tile--now {
          background: #3730a3 !important; /* Tailwind Indigo-800 */
          color: #a5b4fc !important; /* Tailwind Indigo-300 */
        }
        .custom-calendar-theme .react-calendar__tile:enabled:hover,
        .custom-calendar-theme .react-calendar__tile:enabled:focus {
          background-color: #c7d2fe; /* Tailwind Indigo-200 */
        }
        .dark .custom-calendar-theme .react-calendar__tile:enabled:hover,
        .dark .custom-calendar-theme .react-calendar__tile:enabled:focus {
          background-color: #4338ca; /* Tailwind Indigo-700 */
        }
        .custom-calendar-theme .react-calendar__navigation button {
          color: #4f46e5; /* Tailwind Indigo-700 */
          font-weight: bold;
        }
        .dark .custom-calendar-theme .react-calendar__navigation button {
          color: #a5b4fc; /* Tailwind Indigo-300 */
        }
        .custom-calendar-theme .react-calendar__month-view__days__day--weekend {
           /* color: #ef4444; Tailwind Red-500 (Example, if you want to style weekends) */
        }
        .dark .custom-calendar-theme .react-calendar__month-view__days__day--weekend {
           /* color: #f87171; Tailwind Red-400 (Example for dark) */
        }
        .custom-calendar-theme abbr[title] { /* Remove underline from day numbers */
            text-decoration: none;
        }
        .custom-calendar-theme .react-calendar__tile {
            position: relative; /* For badge positioning */
        }
      `}</style>

      {/* Removed the separate "Directions" section with <style jsx> as its content is now in SwipeInstructionFooter using Tailwind */}
    </div>
  );
}
