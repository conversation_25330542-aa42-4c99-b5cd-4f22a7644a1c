import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON>, CheckCircle, Filter, X, CalendarDays, Zap, Plus, Upload, ChevronDown, Settings,
  ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown
} from 'lucide-react';
import <PERSON> from 'papaparse';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css'; // Default styling for react-calendar
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, parseISO, startOfDay, addDays } from 'date-fns';

// --- utils/fsrs.js ---
// FSRS Algorithm Implementation (No changes to core logic, conceptually moved)
class FSRS {
  constructor() {
    this.w = [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61]; // Default weights
  }
  initStability(grade) { return Math.max(this.w[grade - 1], 0.1); }
  initDifficulty(grade) { return Math.min(Math.max(this.w[4] - this.w[5] * (grade - 3), 1), 10); }
  nextInterval(stability) {
    const requestRetention = 0.9;
    return Math.max(1, Math.round(stability * Math.log(requestRetention) / Math.log(0.5)));
  }
  nextStability(difficulty, stability, retrievability, grade) {
    const hardPenalty = grade === 2 ? this.w[15] : 1;
    const easyBonus = grade === 4 ? this.w[16] : 1;
    if (grade === 1) {
      return this.w[11] * Math.pow(difficulty, -this.w[12]) * (Math.pow(stability + 1, this.w[13]) - 1) * Math.exp((1 - retrievability) * this.w[14]);
    } else {
      return stability * (1 + Math.exp(this.w[8]) * (11 - difficulty) * Math.pow(stability, -this.w[9]) * (Math.exp((1 - retrievability) * this.w[10]) - 1)) * hardPenalty * easyBonus;
    }
  }
  nextDifficulty(difficulty, grade) {
    const newDifficulty = difficulty - this.w[6] * (grade - 3);
    return Math.min(Math.max(newDifficulty, 1), 10);
  }
  forgettingCurve(elapsedDays, stability) { return Math.pow(1 + elapsedDays / (9 * stability), -1); }
  schedule(card, grade, reviewDate = new Date()) {
    const elapsedDays = card.lastReview ? (reviewDate.getTime() - new Date(card.lastReview).getTime()) / (1000 * 60 * 60 * 24) : 0;
    let { difficulty, stability } = card;
    if (card.state === 'new') {
      difficulty = this.initDifficulty(grade);
      stability = this.initStability(grade);
    } else {
      const retrievability = this.forgettingCurve(elapsedDays, stability);
      difficulty = this.nextDifficulty(difficulty, grade);
      stability = this.nextStability(difficulty, stability, retrievability, grade);
    }
    const interval = this.nextInterval(stability);
    const dueDate = addDays(reviewDate, interval);
    return {
      ...card,
      difficulty: Math.round(difficulty * 100) / 100,
      stability: Math.round(stability * 100) / 100,
      interval,
      dueDate: format(dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      lastReview: format(reviewDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      reviewCount: (card.reviewCount || 0) + 1,
      state: grade === 1 ? 'learning' : (grade === 2 ? 'relearning' : 'review')
    };
  }
}

// --- data/initialCards.js ---
// (Conceptually moved)
const initialCardsData = [
  { id: 1, question: "What is `static` in Java?", answer: "Belongs to the class, not instances.", category: "Keywords", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
  { id: 2, question: "Java `ArrayList` vs `LinkedList`?", answer: "ArrayList: dynamic array, fast random access. LinkedList: doubly-linked list, fast insertion/deletion.", category: "Collections", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
];

// --- components/JavaCodeHighlighter.jsx ---
// (Conceptually moved, no changes to component itself)
const JavaCodeHighlighter = ({ code, className = "" }) => (
  <pre className={`bg-gray-800 text-gray-200 p-4 rounded-lg overflow-x-auto text-sm font-mono ${className}`}>
    <code>{code || ''}</code>
  </pre>
);

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }, // Changed from blue to sky to match icon
];

// --- components/AnimatedCard.jsx ---
// (Conceptually moved and refactored)
const AnimatedCard = ({ card, onSwipe, isTopCard, style }) => {
  const [showAnswer, setShowAnswer] = useState(false);
  const [{ x, y, rotateZ, scale, opacity }, api] = useSpring(() => ({
    x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1,
    config: { tension: 300, friction: 30 }
  }));

  useEffect(() => {
    setShowAnswer(false);
    api.start({ x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1, immediate: true });
  }, [card, api]);

  const bind = useDrag(({ active, movement: [mx, my], down }) => {
    if (!isTopCard) return;

    const triggerDistance = window.innerWidth / 3.5;
    const isGone = Math.abs(mx) > triggerDistance || Math.abs(my) > triggerDistance;

    if (!active && isGone) {
      let grade = 0;
      if (Math.abs(mx) > Math.abs(my)) { // Horizontal swipe
        grade = mx > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good").grade : GRADING_OPTIONS.find(opt => opt.label === "Again").grade;
      } else { // Vertical swipe
        grade = my > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard").grade : GRADING_OPTIONS.find(opt => opt.label === "Easy").grade;
      }
      onSwipe(card.id, grade);
      return;
    }

    api.start({
      x: active ? mx : 0,
      y: active ? my : 0,
      rotateZ: active ? mx / 20 : 0,
      scale: active ? 1.05 : 1,
      opacity: active ? (isGone ? 0 : 1) : 1,
      immediate: down,
    });
  });

  const getSwipeIndicator = () => {
    if (!isTopCard || (!x.isAnimating && !y.isAnimating && x.get() === 0 && y.get() === 0)) return null;

    const currentX = x.get();
    const currentY = y.get();

    if (Math.abs(currentX) > 30 || Math.abs(currentY) > 30) {
      if (Math.abs(currentX) > Math.abs(currentY)) {
        return currentX > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good") : GRADING_OPTIONS.find(opt => opt.label === "Again");
      } else {
        return currentY > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard") : GRADING_OPTIONS.find(opt => opt.label === "Easy");
      }
    }
    return null;
  };

  const swipeIndicatorOption = getSwipeIndicator();

  if (!card) return null;

  return (
    <animated.div
      {...(isTopCard ? bind() : {})}
      style={{
        ...style,
        x, y, rotateZ, scale, opacity,
        touchAction: isTopCard ? 'none' : 'auto',
      }}
      className="absolute w-full h-full cursor-grab active:cursor-grabbing bg-white dark:bg-slate-800 shadow-2xl rounded-2xl p-6 flex flex-col justify-between border border-slate-200 dark:border-slate-700 select-none"
    >
      {swipeIndicatorOption && isTopCard && (
        <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-opacity duration-200 p-4 rounded-lg flex flex-col items-center gap-1 ${swipeIndicatorOption.feedbackColor} bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm shadow-lg text-2xl font-bold z-10`}>
          <swipeIndicatorOption.icon size={36} className="mb-1" />
          <span>{swipeIndicatorOption.label}</span>
          <swipeIndicatorOption.arrowIcon size={20} className="mt-1 opacity-70" />
        </div>
      )}
      <div className={`transition-opacity duration-300 ${swipeIndicatorOption && isTopCard ? 'opacity-20' : 'opacity-100'}`}>
        <div className="flex justify-between items-start mb-3">
          <span className="text-xs px-3 py-1 bg-indigo-100 dark:bg-indigo-700/30 text-indigo-700 dark:text-indigo-300 rounded-full font-semibold">{card.category}</span>
          <span className="text-xs text-slate-400 dark:text-slate-500">
            S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
          </span>
        </div>
        <div className={`text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none ${showAnswer ? 'pb-4 border-b border-slate-200 dark:border-slate-700' : ''}`}>
          {card.question}
        </div>
        {showAnswer && (
          <div className="text-slate-600 dark:text-slate-300 mt-4 prose-sm md:prose max-w-none overflow-y-auto max-h-48">
            <JavaCodeHighlighter code={card.answer} />
          </div>
        )}
      </div>

      {!showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <button
            onClick={() => setShowAnswer(true)}
            className="w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold text-lg shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:ring-offset-slate-900"
            aria-label="Show Answer (Space)"
            onKeyDown={e => { if (e.key === ' ' || e.key === 'Enter') { e.preventDefault(); setShowAnswer(true); } }}
          >
            Show Answer <span className="ml-2 text-xs text-indigo-200">[Space]</span>
          </button>
        </div>
      )}
      {showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <div className="grid grid-cols-2 gap-2">
            {GRADING_OPTIONS.map(opt => (
              <button
                key={opt.grade}
                className={`bg-${opt.color}-500 text-white py-2 rounded-lg font-semibold shadow hover:bg-${opt.color}-600 focus:outline-none focus:ring-2 focus:ring-${opt.color}-400`}
                onClick={() => onSwipe(card.id, opt.grade)}
                aria-label={`${opt.label} (${opt.shortcut})`}
              >
                <opt.arrowIcon className="inline h-4 w-4 mr-1.5" />
                {opt.label}
                {opt.label.length > 8 ? (
                  <span className="block ml-1 text-xs">[{opt.shortcut}]</span>
                ) : (
                  <span className="ml-1 text-xs">[{opt.shortcut}]</span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </animated.div>
  );
};

// --- hooks/useDarkMode.js ---
// (Conceptually moved)
const useDarkMode = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      const storedTheme = localStorage.getItem('theme');
      setDarkMode(storedTheme === 'dark' || (!storedTheme && prefersDark));
    }
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);

  return [darkMode, setDarkMode];
};

// --- components/AddDropdown.jsx ---
const AddDropdown = ({ onAddCard, onImportCSV }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
        title="Add Content"
      >
        <Plus className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
        <ChevronDown className={`w-4 h-4 text-indigo-600 dark:text-indigo-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 z-20">
            <div className="py-1">
              <button
                onClick={() => {
                  onAddCard();
                  setIsOpen(false);
                }}
                className="flex items-center gap-3 w-full px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add New Card
              </button>
              <button
                onClick={() => {
                  onImportCSV();
                  setIsOpen(false);
                }}
                className="flex items-center gap-3 w-full px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
              >
                <Upload className="w-4 h-4" />
                Import CSV
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// --- components/AppHeader.jsx ---
const AppHeader = ({ selectedCategory, onCategoryChange, categories, onToggleCalendar, onToggleDarkMode, darkMode, studyDeckLength, currentCardOrderInDeck, onAddCard, onImportCSV, onOpenSettings }) => (
  <header className="w-full max-w-3xl mb-6 z-10">
    <div className="flex justify-between items-center mb-4">
      <div className="flex items-center gap-2">
        <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
        <h1 className="text-2xl font-bold">JavaBrain Boost</h1>
      </div>
      <div className="flex items-center gap-3">
        <button onClick={onToggleCalendar} title="Due Dates Calendar" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
          <CalendarDays className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
        </button>
        <button onClick={() => onToggleDarkMode(prev => !prev)} title="Toggle Dark Mode" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
          {darkMode ? <Sparkles className="w-6 h-6 text-yellow-400" /> : <Zap className="w-6 h-6 text-slate-600" />}
        </button>
        <button onClick={onOpenSettings} title="Settings" className="p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
          <Settings className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
        </button>
        <AddDropdown onAddCard={onAddCard} onImportCSV={onImportCSV} />
      </div>
    </div>
    <div className="flex items-center gap-2">
      <Filter className="w-5 h-5 text-slate-500" />
      <select
        value={selectedCategory}
        onChange={onCategoryChange}
        className="px-3 py-1.5 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
      >
        {categories.map(category => (
          <option key={category} value={category}>{category}</option>
        ))}
      </select>
      <span className="text-sm text-slate-500 dark:text-slate-400 ml-auto">
        {studyDeckLength > 0 ? `${currentCardOrderInDeck} / ${studyDeckLength}` : 'No cards due'}
      </span>
    </div>
  </header>
);

// --- components/CardDisplayArea.jsx ---
const CardDisplayArea = ({ studyDeck, currentCardIndex, handleSwipe }) => (
  <main className="w-full max-w-md h-[60vh] md:h-[65vh] relative flex-grow flex items-center justify-center z-0">
    {studyDeck.length === 0 && (
      <div className="text-center text-slate-500 dark:text-slate-400">
        <CheckCircle size={48} className="mx-auto mb-3 text-green-500" />
        <p className="text-xl font-semibold">All clear for now!</p>
        <p>No cards due in the selected category.</p>
      </div>
    )}
    {/* Render only a few cards for performance, top card interactive */}
    {studyDeck.slice(currentCardIndex, currentCardIndex + 3).reverse().map((card, indexInSlice) => {
      const isTopCard = indexInSlice === (studyDeck.slice(currentCardIndex, currentCardIndex + 3).length - 1);
      const cardStyle = { // Stacking effect
        transform: `translateY(${-indexInSlice * 10}px) scale(${1 - indexInSlice * 0.05})`,
        zIndex: studyDeck.length - indexInSlice,
      };
      return (
        <AnimatedCard
          key={card.id}
          card={card}
          onSwipe={handleSwipe}
          isTopCard={isTopCard}
          style={cardStyle}
        />
      );
    })}
  </main>
);

// --- components/SwipeInstructionsSidebar.jsx ---
const SwipeInstructionsSidebar = ({ show }) => {
  if (!show) return null;
  return (
    <div className="hidden xl:block fixed right-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-slate-800 rounded-xl shadow-lg p-4 border border-slate-200 dark:border-slate-700 z-10 max-w-xs">
      <h3 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 text-center">📱 Swipe Directions</h3>
      <div className="space-y-3 text-sm">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300">
          <div className="flex items-center gap-1">
            <span>⬅️</span>
            <ArrowLeft className="h-4 w-4" />
          </div>
          <span className="font-medium">Left: Again</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300">
          <div className="flex items-center gap-1">
            <span>➡️</span>
            <ArrowRight className="h-4 w-4" />
          </div>
          <span className="font-medium">Right: Good</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300">
          <div className="flex items-center gap-1">
            <span>⬇️</span>
            <ArrowDown className="h-4 w-4" />
          </div>
          <span className="font-medium">Down: Hard</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-sky-50 dark:bg-sky-900/20 text-sky-700 dark:text-sky-300">
          <div className="flex items-center gap-1">
            <span>⬆️</span>
            <ArrowUp className="h-4 w-4" />
          </div>
          <span className="font-medium">Up: Easy</span>
        </div>
      </div>
      <div className="mt-4 pt-3 border-t border-slate-200 dark:border-slate-600 text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">⌨️ Or use keys 1-4</p>
      </div>
    </div>
  );
};

// --- components/SwipeInstructionFooter.jsx ---
const SwipeInstructionFooter = ({ show }) => {
  if (!show) return null;
  return (
    <footer className="w-full max-w-md mt-auto text-center py-3 text-xs text-slate-500 dark:text-slate-400 z-10 xl:hidden">
      <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
        <p className="mb-2 font-medium">📱 Swipe Directions:</p>
        <div className="flex flex-wrap justify-center gap-2 text-xs">
          <span className="text-red-600">⬅️ Again</span>
          <span className="text-amber-600">⬇️ Hard</span>
          <span className="text-emerald-600">➡️ Good</span>
          <span className="text-sky-600">⬆️ Easy</span>
        </div>
        <p className="mt-2 text-xs">⌨️ Or use keys 1-4</p>
      </div>
    </footer>
  );
};

// --- components/AddCardModal.jsx ---
const AddCardModal = ({ isOpen, onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: 'Basic Concepts'
  });

  const categories = ['Basic Concepts', 'Keywords', 'Collections', 'OOP', 'Advanced'];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.question.trim() && formData.answer.trim()) {
      onAdd(formData);
      setFormData({ question: '', answer: '', category: 'Basic Concepts' });
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-2xl max-w-md w-full" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-indigo-700 dark:text-indigo-300">Add New Card</h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700">
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Category
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Question
            </label>
            <textarea
              value={formData.question}
              onChange={(e) => setFormData({ ...formData, question: e.target.value })}
              placeholder="Enter your question..."
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              rows={3}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Answer
            </label>
            <textarea
              value={formData.answer}
              onChange={(e) => setFormData({ ...formData, answer: e.target.value })}
              placeholder="Enter the answer..."
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              rows={4}
              required
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-slate-600 dark:text-slate-400 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
            >
              Add Card
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// --- components/SettingsModal.jsx ---
const SettingsModal = ({ isOpen, onClose, intervalSettings, onSettingsChange }) => {
  const [localSettings, setLocalSettings] = useState(intervalSettings);

  useEffect(() => {
    setLocalSettings(intervalSettings);
  }, [intervalSettings]);

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const formatTime = (value, unit) => {
    if (unit === 'minutes') {
      if (value < 60) return `${value}m`;
      const hours = Math.floor(value / 60);
      const minutes = value % 60;
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${value} day${value !== 1 ? 's' : ''}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-lg w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Interval Settings</h3>
              <p className="text-sm text-slate-500 dark:text-slate-400">Customize your learning intervals</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 rounded-xl hover:bg-slate-200/70 dark:hover:bg-slate-700/70 transition-all duration-200 group">
            <X size={20} className="text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300" />
          </button>
        </div>

        <div className="space-y-8">
          {/* Again Interval */}
          <div className="bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-950/30 dark:to-rose-950/30 p-6 rounded-2xl border border-red-200/50 dark:border-red-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-md">
                <ArrowLeft className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-red-800 dark:text-red-200">Again Interval</h4>
                <p className="text-xs text-red-600 dark:text-red-400">Cards you need to review immediately</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-red-700 dark:text-red-300">{formatTime(localSettings.again, 'minutes')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-red-600 dark:text-red-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="1"
                  max="1440"
                  step="1"
                  value={localSettings.again}
                  onChange={(e) => setLocalSettings({...localSettings, again: parseInt(e.target.value)})}
                  className="w-full h-3 bg-red-200 dark:bg-red-900/50 rounded-lg appearance-none cursor-pointer slider-red shadow-inner"
                />
                <div className="flex justify-between text-xs text-red-500 dark:text-red-400 mt-1">
                  <span>1m</span>
                  <span>24h</span>
                </div>
              </div>
            </div>
          </div>

          {/* Hard Interval */}
          <div className="bg-gradient-to-br from-amber-50 to-orange-100 dark:from-amber-950/30 dark:to-orange-950/30 p-6 rounded-2xl border border-amber-200/50 dark:border-amber-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg shadow-md">
                <ArrowDown className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-amber-800 dark:text-amber-200">Hard Interval</h4>
                <p className="text-xs text-amber-600 dark:text-amber-400">Cards that were challenging to recall</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-amber-700 dark:text-amber-300">{formatTime(localSettings.hard, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-amber-600 dark:text-amber-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  value={localSettings.hard}
                  onChange={(e) => setLocalSettings({...localSettings, hard: parseInt(e.target.value)})}
                  className="w-full h-3 bg-amber-200 dark:bg-amber-900/50 rounded-lg appearance-none cursor-pointer slider-amber shadow-inner"
                />
                <div className="flex justify-between text-xs text-amber-500 dark:text-amber-400 mt-1">
                  <span>1 day</span>
                  <span>3 days</span>
                </div>
              </div>
            </div>
          </div>

          {/* Good Interval */}
          <div className="bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/30 dark:to-green-950/30 p-6 rounded-2xl border border-emerald-200/50 dark:border-emerald-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-md">
                <ArrowRight className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-emerald-800 dark:text-emerald-200">Good Interval</h4>
                <p className="text-xs text-emerald-600 dark:text-emerald-400">Cards you recalled correctly</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-emerald-700 dark:text-emerald-300">{formatTime(localSettings.good, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-emerald-600 dark:text-emerald-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="3"
                  max="10"
                  step="1"
                  value={localSettings.good}
                  onChange={(e) => setLocalSettings({...localSettings, good: parseInt(e.target.value)})}
                  className="w-full h-3 bg-emerald-200 dark:bg-emerald-900/50 rounded-lg appearance-none cursor-pointer slider-emerald shadow-inner"
                />
                <div className="flex justify-between text-xs text-emerald-500 dark:text-emerald-400 mt-1">
                  <span>3 days</span>
                  <span>10 days</span>
                </div>
              </div>
            </div>
          </div>

          {/* Easy Interval */}
          <div className="bg-gradient-to-br from-sky-50 to-blue-100 dark:from-sky-950/30 dark:to-blue-950/30 p-6 rounded-2xl border border-sky-200/50 dark:border-sky-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-md">
                <ArrowUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-sky-800 dark:text-sky-200">Easy Interval</h4>
                <p className="text-xs text-sky-600 dark:text-sky-400">Cards you recalled effortlessly</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-sky-700 dark:text-sky-300">{formatTime(localSettings.easy, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-sky-600 dark:text-sky-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="10"
                  max="30"
                  step="1"
                  value={localSettings.easy}
                  onChange={(e) => setLocalSettings({...localSettings, easy: parseInt(e.target.value)})}
                  className="w-full h-3 bg-sky-200 dark:bg-sky-900/50 rounded-lg appearance-none cursor-pointer slider-sky shadow-inner"
                />
                <div className="flex justify-between text-xs text-sky-500 dark:text-sky-400 mt-1">
                  <span>10 days</span>
                  <span>30 days</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-4 pt-8 mt-8 border-t border-slate-200/50 dark:border-slate-700/50">
          <button
            type="button"
            onClick={onClose}
            className="flex-1 px-6 py-3 text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
};

// --- components/CalendarModal.jsx ---
const CalendarModal = ({ isOpen, onClose, dueCountsByDate }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-white dark:bg-slate-800 p-4 rounded-xl shadow-2xl max-w-sm w-full" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold text-indigo-700 dark:text-indigo-300">Due Dates Calendar</h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-slate-200 dark:hover:bg-slate-700"><X size={20}/></button>
        </div>
        <Calendar
          className="!border-none custom-calendar-theme" // Apply custom theme class
          tileContent={({ date, view }) => {
            if (view === 'month') {
              const dateStr = format(date, 'yyyy-MM-dd');
              const count = dueCountsByDate[dateStr];
              if (count > 0) {
                return <span className="absolute bottom-1 right-1 text-xs bg-indigo-500 text-white rounded-full w-4 h-4 flex items-center justify-center">{count}</span>;
              }
            }
            return null;
          }}
        />
      </div>
    </div>
  );
};


// --- JavaFlashcardApp.jsx (Main Component) ---
export default function JavaFlashcardApp() {
  const [cards, setCards] = useState(initialCardsData);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [fsrs] = useState(() => new FSRS()); // Initialize fsrs instance once
  const [showCalendar, setShowCalendar] = useState(false);
  const [showAddCard, setShowAddCard] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [darkMode, setDarkMode] = useDarkMode();

  // FSRS interval settings (in minutes for Again, days for others)
  const [intervalSettings, setIntervalSettings] = useState(() => {
    const saved = localStorage.getItem('fsrsIntervalSettings');
    return saved ? JSON.parse(saved) : {
      again: 1,      // 1 minute
      hard: 1,       // 1 day
      good: 4,       // 4 days
      easy: 15       // 15 days
    };
  });

  const getDueCards = useMemo(() => {
    const now = startOfDay(new Date());
    return cards.filter(card =>
      card.state === 'new' || (card.dueDate && startOfDay(parseISO(card.dueDate)) <= now)
    ).sort((a,b) => (a.dueDate && b.dueDate) ? parseISO(a.dueDate) - parseISO(b.dueDate) : (a.dueDate ? -1 : 1));
  }, [cards]);

  const studyDeck = useMemo(() => {
    return getDueCards.filter(card =>
      selectedCategory === 'All Categories' || card.category === selectedCategory
    );
  }, [getDueCards, selectedCategory]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const getCategories = useMemo(() => {
    const uniqueCategories = [...new Set(cards.map(card => card.category).filter(Boolean))];
    return ['All Categories', ...uniqueCategories.sort()];
  }, [cards]);

  const dueCountsByDate = useMemo(() => {
    const counts = {};
    cards.forEach(card => {
      if (card.dueDate) {
        const dateStr = format(startOfDay(parseISO(card.dueDate)), 'yyyy-MM-dd');
        counts[dateStr] = (counts[dateStr] || 0) + 1;
      }
    });
    return counts;
  }, [cards]);

  const handleGrade = useCallback((cardId, grade) => {
    const cardToGrade = cards.find(c => c.id === cardId);
    if (!cardToGrade) return;

    const updatedCard = fsrs.schedule(cardToGrade, grade);
    setCards(prevCards => prevCards.map(c => (c.id === cardId ? updatedCard : c)));

    setCurrentCardIndex(prev => {
        // Check against the most up-to-date studyDeck length that will be recalculated
        // This logic might need slight adjustment if studyDeck recalculation is deferred
        // For now, assuming studyDeck updates quickly enough.
        // A more robust way might be to recalculate next index based on new studyDeck.
      if (prev < studyDeck.length -1) { // If there are more cards in the current filtered deck
        return prev + 1;
      }
      return 0; // Loop back or handle completion
    });

  }, [cards, fsrs, studyDeck.length]); // studyDeck.length is a dependency, ensure it's stable or causes intended recalculations


  useEffect(() => {
    // Reset currentCardIndex if studyDeck changes (e.g., due to category filter)
    // to avoid out-of-bounds errors.
    setCurrentCardIndex(0);
  }, [studyDeck.length, selectedCategory]); // Depends on selectedCategory as well as it affects studyDeck

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showCalendar || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;
      if (!currentDisplayCard) return;

      // Find the grading option based on key press
      const gradingOpt = GRADING_OPTIONS.find(opt => opt.shortcut === e.key);
      if (gradingOpt) {
        e.preventDefault(); // Prevent default browser actions for '1'-'4'
        handleGrade(currentDisplayCard.id, gradingOpt.grade);
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentDisplayCard, showCalendar, handleGrade]);

  const handleAddCard = useCallback((cardData) => {
    const newCard = {
      id: Math.max(...cards.map(c => c.id), 0) + 1,
      question: cardData.question,
      answer: cardData.answer,
      category: cardData.category,
      difficulty: 5,
      stability: 2.5,
      state: 'new',
      reviewCount: 0,
      lastReview: null,
      dueDate: null
    };
    setCards(prevCards => [...prevCards, newCard]);
  }, [cards]);

  const handleImportCSV = useCallback(() => {
    // Show format information before opening file dialog
    const proceed = window.confirm(
      'CSV Import Format:\n\n' +
      'Required columns: "question", "answer"\n' +
      'Optional column: "category"\n\n' +
      'Example:\n' +
      'question,answer,category\n' +
      '"What is a class?","A blueprint for objects","OOP"\n' +
      '"What is inheritance?","A way to create new classes based on existing ones","OOP"\n\n' +
      'Click OK to select your CSV file.'
    );

    if (!proceed) return;

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        Papa.parse(file, {
          header: true,
          complete: (results) => {
            const newCards = results.data
              .filter(row => row.question && row.answer) // Filter out empty rows
              .map((row, index) => ({
                id: Math.max(...cards.map(c => c.id), 0) + index + 1,
                question: row.question || '',
                answer: row.answer || '',
                category: row.category || 'Imported',
                difficulty: 5,
                stability: 2.5,
                state: 'new',
                reviewCount: 0,
                lastReview: null,
                dueDate: null
              }));

            if (newCards.length > 0) {
              setCards(prevCards => [...prevCards, ...newCards]);
              alert(`Successfully imported ${newCards.length} cards!`);
            } else {
              alert('No valid cards found in the CSV file. Please ensure it has "question" and "answer" columns.');
            }
          },
          error: (error) => {
            alert('Error parsing CSV file: ' + error.message);
          }
        });
      }
    };
    input.click();
  }, [cards]);

  const handleSettingsChange = useCallback((newSettings) => {
    setIntervalSettings(newSettings);
    // Here you could also save to localStorage if you want persistence
    localStorage.setItem('fsrsIntervalSettings', JSON.stringify(newSettings));
  }, []);

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    // setCurrentCardIndex(0); // Already handled by useEffect depending on studyDeck.length
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''} bg-slate-100 dark:bg-slate-900 text-slate-800 dark:text-slate-200 transition-colors duration-300 flex flex-col items-center p-4 relative overflow-hidden`}>
      <AppHeader
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        categories={getCategories}
        onToggleCalendar={() => setShowCalendar(true)}
        onToggleDarkMode={setDarkMode}
        darkMode={darkMode}
        studyDeckLength={studyDeck.length}
        currentCardOrderInDeck={studyDeck.length > 0 ? currentCardIndex + 1 : 0}
        onAddCard={() => setShowAddCard(true)}
        onImportCSV={handleImportCSV}
        onOpenSettings={() => setShowSettings(true)}
      />

      <CardDisplayArea
        studyDeck={studyDeck}
        currentCardIndex={currentCardIndex}
        handleSwipe={handleGrade} // handleGrade also serves as handleSwipe
      />

      <SwipeInstructionsSidebar show={!!currentDisplayCard} />
      <SwipeInstructionFooter show={!!currentDisplayCard} />

      <CalendarModal
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
        dueCountsByDate={dueCountsByDate}
      />

      <AddCardModal
        isOpen={showAddCard}
        onClose={() => setShowAddCard(false)}
        onAdd={handleAddCard}
      />

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        intervalSettings={intervalSettings}
        onSettingsChange={handleSettingsChange}
      />

      {/* Global Styles for react-calendar (scoped with a custom class) */}
      {/* It's generally better to put these in a global CSS file if possible,
          but for self-contained components this <style jsx global> pattern is common. */}
      <style jsx global>{`
        .custom-calendar-theme .react-calendar__tile--now {
          background: #e0e7ff !important; /* Tailwind Indigo-100 */
          color: #4f46e5 !important; /* Tailwind Indigo-700 */
          font-weight: bold;
        }
        .dark .custom-calendar-theme .react-calendar__tile--now {
          background: #3730a3 !important; /* Tailwind Indigo-800 */
          color: #a5b4fc !important; /* Tailwind Indigo-300 */
        }
        .custom-calendar-theme .react-calendar__tile:enabled:hover,
        .custom-calendar-theme .react-calendar__tile:enabled:focus {
          background-color: #c7d2fe; /* Tailwind Indigo-200 */
        }
        .dark .custom-calendar-theme .react-calendar__tile:enabled:hover,
        .dark .custom-calendar-theme .react-calendar__tile:enabled:focus {
          background-color: #4338ca; /* Tailwind Indigo-700 */
        }
        .custom-calendar-theme .react-calendar__navigation button {
          color: #4f46e5; /* Tailwind Indigo-700 */
          font-weight: bold;
        }
        .dark .custom-calendar-theme .react-calendar__navigation button {
          color: #a5b4fc; /* Tailwind Indigo-300 */
        }
        .custom-calendar-theme .react-calendar__month-view__days__day--weekend {
           /* color: #ef4444; Tailwind Red-500 (Example, if you want to style weekends) */
        }
        .dark .custom-calendar-theme .react-calendar__month-view__days__day--weekend {
           /* color: #f87171; Tailwind Red-400 (Example for dark) */
        }
        .custom-calendar-theme abbr[title] { /* Remove underline from day numbers */
            text-decoration: none;
        }
        .custom-calendar-theme .react-calendar__tile {
            position: relative; /* For badge positioning */
        }
      `}</style>

      {/* Removed the separate "Directions" section with <style jsx> as its content is now in SwipeInstructionFooter using Tailwind */}
    </div>
  );
}
