import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON>, CheckCircle, Filter, X, CalendarDays, Zap, Plus, Upload, ChevronDown, Settings,
  ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, Arrow<PERSON>p, ArrowDown
} from 'lucide-react';
import <PERSON> from 'papaparse';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css'; // Default styling for react-calendar
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, parseISO, addDays } from 'date-fns';

// --- utils/fsrs.js ---
// FSRS Algorithm Implementation (No changes to core logic, conceptually moved)
class FSRS {
  constructor() {
    this.w = [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61]; // Default weights
  }
  initStability(grade) { return Math.max(this.w[grade - 1], 0.1); }
  initDifficulty(grade) { return Math.min(Math.max(this.w[4] - this.w[5] * (grade - 3), 1), 10); }
  nextInterval(stability) {
    const requestRetention = 0.9;
    return Math.max(1, Math.round(stability * Math.log(requestRetention) / Math.log(0.5)));
  }
  nextStability(difficulty, stability, retrievability, grade) {
    const hardPenalty = grade === 2 ? this.w[15] : 1;
    const easyBonus = grade === 4 ? this.w[16] : 1;
    if (grade === 1) {
      return this.w[11] * Math.pow(difficulty, -this.w[12]) * (Math.pow(stability + 1, this.w[13]) - 1) * Math.exp((1 - retrievability) * this.w[14]);
    } else {
      return stability * (1 + Math.exp(this.w[8]) * (11 - difficulty) * Math.pow(stability, -this.w[9]) * (Math.exp((1 - retrievability) * this.w[10]) - 1)) * hardPenalty * easyBonus;
    }
  }
  nextDifficulty(difficulty, grade) {
    const newDifficulty = difficulty - this.w[6] * (grade - 3);
    return Math.min(Math.max(newDifficulty, 1), 10);
  }
  forgettingCurve(elapsedDays, stability) { return Math.pow(1 + elapsedDays / (9 * stability), -1); }
  schedule(card, grade, reviewDate = new Date()) {
    const elapsedDays = card.lastReview ? (reviewDate.getTime() - new Date(card.lastReview).getTime()) / (1000 * 60 * 60 * 24) : 0;
    let { difficulty, stability } = card;
    if (card.state === 'new') {
      difficulty = this.initDifficulty(grade);
      stability = this.initStability(grade);
    } else {
      const retrievability = this.forgettingCurve(elapsedDays, stability);
      difficulty = this.nextDifficulty(difficulty, grade);
      stability = this.nextStability(difficulty, stability, retrievability, grade);
    }
    const interval = this.nextInterval(stability);
    const dueDate = addDays(reviewDate, interval);
    return {
      ...card,
      difficulty: Math.round(difficulty * 100) / 100,
      stability: Math.round(stability * 100) / 100,
      interval,
      dueDate: format(dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      lastReview: format(reviewDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      reviewCount: (card.reviewCount || 0) + 1,
      state: grade === 1 ? 'learning' : (grade === 2 ? 'relearning' : 'review')
    };
  }
}

// --- data/initialCards.js ---
// (Conceptually moved)
const initialCardsData = [
  { id: 1, question: "What is `static` in Java?", answer: "Belongs to the class, not instances.", category: "Keywords", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
  { id: 2, question: "Java `ArrayList` vs `LinkedList`?", answer: "ArrayList: dynamic array, fast random access. LinkedList: doubly-linked list, fast insertion/deletion.", category: "Collections", difficulty: 5, stability: 2.5, state: 'new', reviewCount: 0, lastReview: null, dueDate: null },
];

// --- components/JavaCodeHighlighter.jsx ---
// --- components/JavaCodeHighlighter.jsx ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Simple syntax highlighting for Java keywords and common patterns
  const highlightJava = (text) => {
    // Java keywords
    const keywords = [
      'public', 'private', 'protected', 'static', 'final', 'abstract', 'class', 'interface',
      'extends', 'implements', 'import', 'package', 'void', 'int', 'String', 'boolean',
      'double', 'float', 'long', 'char', 'byte', 'short', 'if', 'else', 'for', 'while',
      'do', 'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
      'finally', 'throw', 'throws', 'new', 'this', 'super', 'null', 'true', 'false'
    ];

    let highlighted = text;

    // Highlight keywords
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(regex, `<span class="text-blue-400 font-semibold">${keyword}</span>`);
    });

    // Highlight strings
    highlighted = highlighted.replace(/"([^"]*?)"/g, '<span class="text-green-400">"$1"</span>');
    highlighted = highlighted.replace(/'([^']*?)'/g, '<span class="text-green-400">\'$1\'</span>');

    // Highlight comments
    highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');

    // Highlight numbers
    highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="text-yellow-400">$&</span>');

    // Highlight method calls and class names (capitalized words)
    highlighted = highlighted.replace(/\b[A-Z][a-zA-Z0-9]*\b/g, '<span class="text-cyan-400">$&</span>');

    return highlighted;
  };

  // Check if the content looks like code (contains common programming patterns)
  const looksLikeCode = (text) => {
    const codePatterns = [
      /\{[\s\S]*\}/, // curly braces
      /\([\s\S]*\)/, // parentheses with content
      /;[\s]*$/, // semicolon at end of line
      /\bpublic\b|\bprivate\b|\bclass\b|\bvoid\b|\bint\b|\bString\b/, // Java keywords
      /\w+\s*\(.*\)/, // method calls
      /\w+\.\w+/, // dot notation
    ];

    return codePatterns.some(pattern => pattern.test(text));
  };

  const isCode = looksLikeCode(code);

  if (isCode) {
    return (
      <div className={`bg-gradient-to-br from-slate-900 to-slate-800 border border-slate-700 rounded-lg overflow-hidden h-full flex flex-col ${className}`}>
        <div className="bg-gradient-to-r from-violet-600 to-purple-600 px-4 py-2 text-white text-xs font-semibold flex items-center gap-2 flex-shrink-0">
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          </div>
          <span>Java Code</span>
        </div>
        <pre className="p-4 overflow-x-auto text-base font-mono leading-loose whitespace-pre-wrap flex-1 min-h-0">
          <code
            className="text-gray-200 block"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'pre-wrap',
              lineHeight: '1.8'
            }}
            dangerouslySetInnerHTML={{ __html: highlightJava(code) }}
          />
        </pre>
      </div>
    );
  } else {
    // Regular text formatting with better typography and intelligent line breaking
    const formatTextContent = (text) => {
      // Pre-process text to ensure numbered lists and bullet points are properly separated
      let processedText = text
        // Ensure each numbered item starts on a new line
        .replace(/(\d+\.\s+)/g, '\n$1')
        // Ensure each lettered item starts on a new line
        .replace(/([a-zA-Z]\.\s+)/g, '\n$1')
        // Ensure each bullet point starts on a new line
        .replace(/([-•*]\s+)/g, '\n$1')
        // Clean up multiple newlines to allow for correct paragraph splitting
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        // Remove leading newlines
        .replace(/^\n+/, '');

      // Split into paragraphs first. Use a regex that handles both single and double newlines for paragraph separation.
      const paragraphs = processedText.split(/(?:\r?\n\s*){2,}/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        // Check for list patterns within the paragraph
        const listPattern = /(?:^|\n)[\s]*(?:[-•*]|\d+\.|[a-zA-Z]\.)\s*/;
        const isListLike = listPattern.test(cleanParagraph);

        if (isListLike) {
          // Split into individual list items
          const listItems = cleanParagraph.split(listPattern).filter(item => item.trim());

          return (
            <ul key={pIndex} className="list-none p-0 m-0 mb-4 last:mb-0 space-y-2"> {/* Tailwind spacing for lists */}
              {listItems.map((item, iIndex) => {
                const trimmedItem = item.trim();
                if (!trimmedItem) return null;

                // Re-add the prefix if it was split off (simple re-construction)
                const prefixMatch = item.match(/^([\s]*(?:[-•*]|\d+\.|[a-zA-Z]\.)\s*)/);
                const prefix = prefixMatch ? prefixMatch[1] : '';

                return (
                  <li key={`${pIndex}-${iIndex}`} className="flex items-start text-base leading-relaxed text-slate-700 dark:text-slate-300">
                    <span className="flex-shrink-0 mr-2 text-violet-500 dark:text-purple-400 font-semibold">
                      {prefix.trim().replace(/^\d+\./, (match) => `${parseInt(match)}.`)} {/* Ensure numbers are re-parsed for consistent numbering if needed */}
                    </span>
                    <span>{trimmedItem.replace(/^[\s]*(?:[-•*]|\d+\.|[a-zA-Z]\.)\s*/, '')}</span> {/* Remove prefixes from content after re-adding */}
                  </li>
                );
              }).filter(Boolean)}
            </ul>
          );
        } else {
          // For non-list paragraphs, use a general paragraph styling
          return (
            <p key={pIndex} className="mb-4 last:mb-0 text-base leading-relaxed text-slate-700 dark:text-slate-300">
              {cleanParagraph}
            </p>
          );
        }
      }).filter(Boolean);
    };

    return (
      <div className={`bg-gradient-to-br from-white to-slate-50 dark:from-slate-700 dark:to-slate-800 rounded-lg p-4 h-full ${className}`}>
        <div className="prose prose-base dark:prose-invert max-w-none h-full">
          <div
            className="text-slate-700 dark:text-slate-300 h-full"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              hyphens: 'auto'
            }}
          >
            {formatTextContent(code)}
          </div>
        </div>
      </div>
    );
  }
};

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }
];

// --- components/AnimatedCard.jsx ---
// (Conceptually moved and refactored)
// --- components/AnimatedCard.jsx ---
// (Conceptually moved and refactored)
const AnimatedCard = ({ card, onSwipe, isTopCard, style }) => {
  const [showAnswer, setShowAnswer] = useState(false);
  const [{ x, y, rotateZ, scale, opacity }, api] = useSpring(() => ({
    x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1,
    config: { tension: 300, friction: 30 }
  }));

  useEffect(() => {
    setShowAnswer(false);
    api.start({ x: 0, y: 0, rotateZ: 0, scale: 1, opacity: 1, immediate: true });
  }, [card, api]);

  const bind = useDrag(({ active, movement: [mx, my], down }) => {
    if (!isTopCard) return;

    const triggerDistance = window.innerWidth / 3.5;
    const isGone = Math.abs(mx) > triggerDistance || Math.abs(my) > triggerDistance;

    if (!active && isGone) {
      let grade = 0;
      if (Math.abs(mx) > Math.abs(my)) { // Horizontal swipe
        grade = mx > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good").grade : GRADING_OPTIONS.find(opt => opt.label === "Again").grade;
      } else { // Vertical swipe
        grade = my > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard").grade : GRADING_OPTIONS.find(opt => opt.label === "Easy").grade;
      }
      onSwipe(card.id, grade);
      return;
    }

    api.start({
      x: active ? mx : 0,
      y: active ? my : 0,
      rotateZ: active ? mx / 20 : 0,
      scale: active ? 1.05 : 1,
      opacity: active ? (isGone ? 0 : 1) : 1,
      immediate: down,
    });
  });

  const getSwipeIndicator = () => {
    if (!isTopCard || (!x.isAnimating && !y.isAnimating && x.get() === 0 && y.get() === 0)) return null;

    const currentX = x.get();
    const currentY = y.get();

    if (Math.abs(currentX) > 30 || Math.abs(currentY) > 30) {
      if (Math.abs(currentX) > Math.abs(currentY)) {
        return currentX > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Good") : GRADING_OPTIONS.find(opt => opt.label === "Again");
      } else {
        return currentY > 0 ? GRADING_OPTIONS.find(opt => opt.label === "Hard") : GRADING_OPTIONS.find(opt => opt.label === "Easy");
      }
    }
    return null;
  };

  const swipeIndicatorOption = getSwipeIndicator();

  if (!card) return null;

  return (
    <animated.div
      {...(isTopCard ? bind() : {})}
      style={{
        ...style,
        x, y, rotateZ, scale, opacity,
        touchAction: isTopCard ? 'none' : 'auto',
      }}
      className="absolute w-full h-full cursor-grab active:cursor-grabbing bg-gradient-to-br from-white via-slate-50/50 to-white dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 shadow-2xl rounded-3xl p-6 flex flex-col justify-between border border-violet-200/50 dark:border-purple-700/50 select-none backdrop-blur-sm"
    >
      {swipeIndicatorOption && isTopCard && (
        <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-opacity duration-200 p-4 rounded-lg flex flex-col items-center gap-1 ${swipeIndicatorOption.feedbackColor} bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm shadow-lg text-2xl font-bold z-10`}>
          <swipeIndicatorOption.icon size={36} className="mb-1" />
          <span>{swipeIndicatorOption.label}</span>
          <swipeIndicatorOption.arrowIcon size={20} className="mt-1 opacity-70" />
        </div>
      )}
      <div className={`transition-opacity duration-300 ${swipeIndicatorOption && isTopCard ? 'opacity-20' : 'opacity-100'}`}>
        <div className="flex justify-between items-start mb-4">
          <span className="text-xs px-3 py-1.5 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full font-semibold shadow-md">{card.category}</span>
          <span className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100/50 dark:bg-slate-700/50 px-2 py-1 rounded-lg">
            S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
          </span>
        </div>
        <div className={`text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none whitespace-pre-wrap break-words leading-relaxed ${showAnswer ? 'pb-4 border-b border-slate-200 dark:border-slate-700' : ''}`}>
          {card.question}
        </div>
        {showAnswer && (
          <div className="mt-4 flex-1 flex flex-col min-h-0"> {/* Add flex-1 and min-h-0 here */}
            <div className="flex-1 overflow-y-auto border border-violet-200/50 dark:border-purple-700/50 rounded-xl bg-slate-50/50 dark:bg-slate-800/50 p-1"> {/* Remove fixed height */}
              <JavaCodeHighlighter code={card.answer} />
            </div>
          </div>
        )}
      </div>

      {!showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <button
            onClick={() => setShowAnswer(true)}
            className="w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white py-4 rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 font-bold text-lg shadow-xl hover:shadow-2xl transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 dark:ring-offset-slate-900"
            aria-label="Show Answer (Space)"
            onKeyDown={e => { if (e.key === ' ' || e.key === 'Enter') { e.preventDefault(); setShowAnswer(true); } }}
          >
            ✨ Show Answer <span className="ml-2 text-xs text-purple-200 bg-white/20 px-2 py-1 rounded-full">[Space]</span>
          </button>
        </div>
      )}
      {showAnswer && (
        <div className="flex flex-col gap-2 mt-auto">
          <div className="grid grid-cols-2 gap-3">
            {GRADING_OPTIONS.map(opt => (
              <button
                key={opt.grade}
                className={`bg-gradient-to-r ${
                  opt.color === 'red' ? 'from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700' :
                  opt.color === 'amber' ? 'from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700' :
                  opt.color === 'emerald' ? 'from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700' :
                  'from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700'
                } text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50`}
                onClick={() => onSwipe(card.id, opt.grade)}
                aria-label={`${opt.label} (${opt.shortcut})`}
              >
                <opt.arrowIcon className="inline h-4 w-4 mr-1.5" />
                {opt.label}
                {opt.label.length > 8 ? (
                  <span className="block ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[{opt.shortcut}]</span>
                ) : (
                  <span className="ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[{opt.shortcut}]</span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </animated.div>
  );
};

// --- hooks/useDarkMode.js ---
// (Conceptually moved)
const useDarkMode = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      const storedTheme = localStorage.getItem('theme');
      setDarkMode(storedTheme === 'dark' || (!storedTheme && prefersDark));
    }
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);

  return [darkMode, setDarkMode];
};

// --- components/AddDropdown.jsx ---
const AddDropdown = ({ onAddCard, onImportCSV }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group"
        title="Add Content"
      >
        <Plus className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
        <ChevronDown className={`w-4 h-4 text-violet-600 dark:text-violet-400 transition-transform group-hover:scale-110 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-52 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 rounded-xl shadow-2xl border border-violet-200/50 dark:border-purple-700/50 z-20 backdrop-blur-sm">
            <div className="p-2">
              <button
                onClick={() => {
                  onAddCard();
                  setIsOpen(false);
                }}
                className="flex items-center gap-3 w-full px-4 py-3 text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 rounded-lg group"
              >
                <div className="p-1.5 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform">
                  <Plus className="w-3 h-3 text-white" />
                </div>
                Add New Card
              </button>
              <button
                onClick={() => {
                  onImportCSV();
                  setIsOpen(false);
                }}
                className="flex items-center gap-3 w-full px-4 py-3 text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 rounded-lg group"
              >
                <div className="p-1.5 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform">
                  <Upload className="w-3 h-3 text-white" />
                </div>
                Import CSV
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// --- components/AppHeader.jsx ---
const AppHeader = ({ selectedCategory, onCategoryChange, categories, onToggleCalendar, onToggleDarkMode, darkMode, studyDeckLength, currentCardOrderInDeck, onAddCard, onImportCSV, onOpenSettings }) => (
  <header className="w-full max-w-4xl mb-8 z-10">
    <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-violet-200/50 dark:border-purple-700/50">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl shadow-lg">
            <Brain className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">JavaBrain Boost</h1>
            <p className="text-sm text-slate-500 dark:text-slate-400">Master Java with spaced repetition</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button onClick={onToggleCalendar} title="Due Dates Calendar" className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group">
            <CalendarDays className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
          </button>
          <button onClick={() => onToggleDarkMode(prev => !prev)} title="Toggle Dark Mode" className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group">
            {darkMode ? <Sparkles className="w-6 h-6 text-yellow-400 group-hover:scale-110 transition-transform" /> : <Zap className="w-6 h-6 text-violet-600 group-hover:scale-110 transition-transform" />}
          </button>
          <button onClick={onOpenSettings} title="Settings" className="p-3 rounded-xl hover:bg-violet-100 dark:hover:bg-purple-800/50 transition-all duration-200 group">
            <Settings className="w-6 h-6 text-violet-600 dark:text-violet-400 group-hover:scale-110 transition-transform" />
          </button>
          <AddDropdown onAddCard={onAddCard} onImportCSV={onImportCSV} />
        </div>
      </div>
      <div className="flex items-center gap-3 bg-slate-50/50 dark:bg-slate-700/50 p-3 rounded-xl">
        <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg">
          <Filter className="w-4 h-4 text-white" />
        </div>
        <select
          value={selectedCategory}
          onChange={onCategoryChange}
          className="flex-1 px-4 py-2 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 shadow-sm appearance-none cursor-pointer outline-none focus:ring-2 focus:ring-violet-500 text-sm font-medium"
        >
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
        <div className="px-3 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-xl text-sm font-bold shadow-md">
          {studyDeckLength > 0 ? `${currentCardOrderInDeck} / ${studyDeckLength}` : 'All Clear! 🎉'}
        </div>
      </div>
    </div>
  </header>
);

// --- components/CardDisplayArea.jsx ---
const CardDisplayArea = ({ studyDeck, currentCardIndex, handleSwipe }) => (
  <main className="w-full max-w-2xl h-[60vh] md:h-[65vh] relative flex-grow flex items-center justify-center z-0">
    {studyDeck.length === 0 && (
      <div className="text-center bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/30 dark:to-green-950/30 p-8 rounded-3xl border border-emerald-200/50 dark:border-emerald-800/30 shadow-xl">
        <div className="p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
          <CheckCircle size={48} className="text-white" />
        </div>
        <h2 className="text-2xl font-bold text-emerald-800 dark:text-emerald-200 mb-2">🎉 All Clear!</h2>
        <p className="text-emerald-600 dark:text-emerald-400 text-lg">No cards due in the selected category.</p>
        <p className="text-emerald-500 dark:text-emerald-500 text-sm mt-2">Great job staying on top of your studies!</p>
      </div>
    )}
    {/* Render only a few cards for performance, top card interactive */}
    {studyDeck.slice(currentCardIndex, currentCardIndex + 3).reverse().map((card, indexInSlice) => {
      const isTopCard = indexInSlice === (studyDeck.slice(currentCardIndex, currentCardIndex + 3).length - 1);
      const cardStyle = { // Stacking effect
        transform: `translateY(${-indexInSlice * 10}px) scale(${1 - indexInSlice * 0.05})`,
        zIndex: studyDeck.length - indexInSlice,
      };
      return (
        <AnimatedCard
          key={card.id}
          card={card}
          onSwipe={handleSwipe}
          isTopCard={isTopCard}
          style={cardStyle}
        />
      );
    })}
  </main>
);

// --- components/SwipeInstructionsSidebar.jsx ---
const SwipeInstructionsSidebar = ({ show }) => {
  if (!show) return null;
  return (
    <div className="hidden xl:block fixed right-4 top-1/2 transform -translate-y-1/2 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 rounded-2xl shadow-2xl p-6 border border-violet-200/50 dark:border-purple-700/50 z-10 max-w-xs backdrop-blur-sm">
      <div className="flex items-center gap-2 mb-4">
        <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg">
          <Sparkles className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-sm font-bold text-slate-700 dark:text-slate-300">Swipe Directions</h3>
      </div>
      <div className="space-y-3 text-sm">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300">
          <div className="flex items-center gap-1">
            <span>⬅️</span>
            <ArrowLeft className="h-4 w-4" />
          </div>
          <span className="font-medium">Left: Again</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300">
          <div className="flex items-center gap-1">
            <span>➡️</span>
            <ArrowRight className="h-4 w-4" />
          </div>
          <span className="font-medium">Right: Good</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300">
          <div className="flex items-center gap-1">
            <span>⬇️</span>
            <ArrowDown className="h-4 w-4" />
          </div>
          <span className="font-medium">Down: Hard</span>
        </div>
        <div className="flex items-center gap-3 p-2 rounded-lg bg-sky-50 dark:bg-sky-900/20 text-sky-700 dark:text-sky-300">
          <div className="flex items-center gap-1">
            <span>⬆️</span>
            <ArrowUp className="h-4 w-4" />
          </div>
          <span className="font-medium">Up: Easy</span>
        </div>
      </div>
      <div className="mt-4 pt-3 border-t border-slate-200 dark:border-slate-600 text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">⌨️ Or use keys 1-4</p>
      </div>
    </div>
  );
};

// --- components/SwipeInstructionFooter.jsx ---
const SwipeInstructionFooter = ({ show }) => {
  if (!show) return null;
  return (
    <footer className="w-full max-w-2xl mt-auto text-center py-4 z-10 xl:hidden">
      <div className="bg-gradient-to-r from-white via-slate-50 to-white dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 rounded-2xl p-4 border border-violet-200/50 dark:border-purple-700/50 shadow-lg backdrop-blur-sm">
        <div className="flex items-center justify-center gap-2 mb-3">
          <div className="p-1.5 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg">
            <Sparkles className="w-3 h-3 text-white" />
          </div>
          <p className="font-bold text-slate-700 dark:text-slate-300 text-sm">Swipe Directions</p>
        </div>
        <div className="flex flex-wrap justify-center gap-3 text-xs">
          <span className="bg-gradient-to-r from-red-500 to-rose-600 text-white px-2 py-1 rounded-full font-medium">⬅️ Again</span>
          <span className="bg-gradient-to-r from-amber-500 to-orange-600 text-white px-2 py-1 rounded-full font-medium">⬇️ Hard</span>
          <span className="bg-gradient-to-r from-emerald-500 to-green-600 text-white px-2 py-1 rounded-full font-medium">➡️ Good</span>
          <span className="bg-gradient-to-r from-sky-500 to-blue-600 text-white px-2 py-1 rounded-full font-medium">⬆️ Easy</span>
        </div>
        <p className="mt-3 text-xs text-slate-500 dark:text-slate-400 bg-slate-100/50 dark:bg-slate-700/50 px-3 py-1 rounded-full inline-block">⌨️ Or use keys 1-4</p>
      </div>
    </footer>
  );
};

// --- components/AddCardModal.jsx ---
const AddCardModal = ({ isOpen, onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: 'Basic Concepts'
  });

  const categories = ['Basic Concepts', 'Keywords', 'Collections', 'OOP', 'Advanced'];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.question.trim() && formData.answer.trim()) {
      onAdd(formData);
      setFormData({ question: '', answer: '', category: 'Basic Concepts' });
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-2xl border border-violet-200/50 dark:border-purple-700/50 max-w-lg w-full" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl shadow-lg">
              <Plus className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">Add New Card</h3>
              <p className="text-sm text-slate-500 dark:text-slate-400">Create a new flashcard</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 rounded-xl hover:bg-slate-200/70 dark:hover:bg-slate-700/70 transition-all duration-200 group">
            <X size={20} className="text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-bold text-slate-700 dark:text-slate-300 mb-2">
              📂 Category
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              className="w-full px-4 py-3 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-violet-500 focus:border-violet-500 shadow-sm font-medium"
            >
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-bold text-slate-700 dark:text-slate-300 mb-2">
              ❓ Question
            </label>
            <textarea
              value={formData.question}
              onChange={(e) => setFormData({ ...formData, question: e.target.value })}
              placeholder="What would you like to learn about?"
              className="w-full px-4 py-3 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-violet-500 focus:border-violet-500 resize-none shadow-sm"
              rows={3}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-bold text-slate-700 dark:text-slate-300 mb-2">
              💡 Answer
            </label>
            <textarea
              value={formData.answer}
              onChange={(e) => setFormData({ ...formData, answer: e.target.value })}
              placeholder="Provide the answer or explanation..."
              className="w-full px-4 py-3 border border-violet-200 dark:border-purple-700 rounded-xl bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 focus:ring-2 focus:ring-violet-500 focus:border-violet-500 resize-none shadow-sm"
              rows={4}
              required
            />
          </div>

          <div className="flex gap-4 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-6 py-3 text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-6 py-3 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-xl hover:from-violet-700 hover:to-purple-700 transition-all duration-200 font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
            >
              ✨ Add Card
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// --- components/SettingsModal.jsx ---
const SettingsModal = ({ isOpen, onClose, intervalSettings, onSettingsChange }) => {
  const [localSettings, setLocalSettings] = useState(intervalSettings);

  useEffect(() => {
    setLocalSettings(intervalSettings);
  }, [intervalSettings]);

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const formatTime = (value, unit) => {
    if (unit === 'minutes') {
      if (value < 60) return `${value}m`;
      const hours = Math.floor(value / 60);
      const minutes = value % 60;
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${value} day${value !== 1 ? 's' : ''}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-lg w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Interval Settings</h3>
              <p className="text-sm text-slate-500 dark:text-slate-400">Customize your learning intervals</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 rounded-xl hover:bg-slate-200/70 dark:hover:bg-slate-700/70 transition-all duration-200 group">
            <X size={20} className="text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300" />
          </button>
        </div>

        <div className="space-y-8">
          {/* Again Interval */}
          <div className="bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-950/30 dark:to-rose-950/30 p-6 rounded-2xl border border-red-200/50 dark:border-red-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-md">
                <ArrowLeft className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-red-800 dark:text-red-200">Again Interval</h4>
                <p className="text-xs text-red-600 dark:text-red-400">Cards you need to review immediately</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-red-700 dark:text-red-300">{formatTime(localSettings.again, 'minutes')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-red-600 dark:text-red-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="1"
                  max="1440"
                  step="1"
                  value={localSettings.again}
                  onChange={(e) => setLocalSettings({...localSettings, again: parseInt(e.target.value)})}
                  className="w-full h-3 bg-red-200 dark:bg-red-900/50 rounded-lg appearance-none cursor-pointer slider-red shadow-inner"
                />
                <div className="flex justify-between text-xs text-red-500 dark:text-red-400 mt-1">
                  <span>1m</span>
                  <span>24h</span>
                </div>
              </div>
            </div>
          </div>

          {/* Hard Interval */}
          <div className="bg-gradient-to-br from-amber-50 to-orange-100 dark:from-amber-950/30 dark:to-orange-950/30 p-6 rounded-2xl border border-amber-200/50 dark:border-amber-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg shadow-md">
                <ArrowDown className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-amber-800 dark:text-amber-200">Hard Interval</h4>
                <p className="text-xs text-amber-600 dark:text-amber-400">Cards that were challenging to recall</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-amber-700 dark:text-amber-300">{formatTime(localSettings.hard, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-amber-600 dark:text-amber-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  value={localSettings.hard}
                  onChange={(e) => setLocalSettings({...localSettings, hard: parseInt(e.target.value)})}
                  className="w-full h-3 bg-amber-200 dark:bg-amber-900/50 rounded-lg appearance-none cursor-pointer slider-amber shadow-inner"
                />
                <div className="flex justify-between text-xs text-amber-500 dark:text-amber-400 mt-1">
                  <span>1 day</span>
                  <span>3 days</span>
                </div>
              </div>
            </div>
          </div>

          {/* Good Interval */}
          <div className="bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/30 dark:to-green-950/30 p-6 rounded-2xl border border-emerald-200/50 dark:border-emerald-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-md">
                <ArrowRight className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-emerald-800 dark:text-emerald-200">Good Interval</h4>
                <p className="text-xs text-emerald-600 dark:text-emerald-400">Cards you recalled correctly</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-emerald-700 dark:text-emerald-300">{formatTime(localSettings.good, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-emerald-600 dark:text-emerald-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="3"
                  max="10"
                  step="1"
                  value={localSettings.good}
                  onChange={(e) => setLocalSettings({...localSettings, good: parseInt(e.target.value)})}
                  className="w-full h-3 bg-emerald-200 dark:bg-emerald-900/50 rounded-lg appearance-none cursor-pointer slider-emerald shadow-inner"
                />
                <div className="flex justify-between text-xs text-emerald-500 dark:text-emerald-400 mt-1">
                  <span>3 days</span>
                  <span>10 days</span>
                </div>
              </div>
            </div>
          </div>

          {/* Easy Interval */}
          <div className="bg-gradient-to-br from-sky-50 to-blue-100 dark:from-sky-950/30 dark:to-blue-950/30 p-6 rounded-2xl border border-sky-200/50 dark:border-sky-800/30 shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-md">
                <ArrowUp className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-sky-800 dark:text-sky-200">Easy Interval</h4>
                <p className="text-xs text-sky-600 dark:text-sky-400">Cards you recalled effortlessly</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex justify-center items-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <span className="text-lg font-bold text-sky-700 dark:text-sky-300">{formatTime(localSettings.easy, 'days')}</span>
              </div>
              <div>
                <label className="text-xs font-medium text-sky-600 dark:text-sky-400 mb-3 block text-center">Interval Duration</label>
                <input
                  type="range"
                  min="10"
                  max="30"
                  step="1"
                  value={localSettings.easy}
                  onChange={(e) => setLocalSettings({...localSettings, easy: parseInt(e.target.value)})}
                  className="w-full h-3 bg-sky-200 dark:bg-sky-900/50 rounded-lg appearance-none cursor-pointer slider-sky shadow-inner"
                />
                <div className="flex justify-between text-xs text-sky-500 dark:text-sky-400 mt-1">
                  <span>10 days</span>
                  <span>30 days</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-4 pt-8 mt-8 border-t border-slate-200/50 dark:border-slate-700/50">
          <button
            type="button"
            onClick={onClose}
            className="flex-1 px-6 py-3 text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
};

// --- components/CalendarModal.jsx ---
const CalendarModal = ({ isOpen, onClose, dueCountsByDate }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-6 rounded-2xl shadow-2xl border border-violet-200/50 dark:border-purple-700/50 max-w-md w-full" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl shadow-lg">
              <CalendarDays className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">Due Dates</h3>
              <p className="text-xs text-slate-500 dark:text-slate-400">Your study schedule</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 rounded-xl hover:bg-slate-200/70 dark:hover:bg-slate-700/70 transition-all duration-200 group">
            <X size={20} className="text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300" />
          </button>
        </div>
        <div className="bg-white dark:bg-slate-700 rounded-xl p-2 shadow-inner">
          <Calendar
            className="!border-none custom-calendar-theme w-full" // Apply custom theme class
            tileContent={({ date, view }) => {
              if (view === 'month') {
                const dateStr = format(date, 'yyyy-MM-dd');
                const count = dueCountsByDate[dateStr];
                if (count > 0) {
                  return <div className="text-xs bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-sm">{count}</div>;
                }
              }
              return null;
            }}
          />
        </div>
      </div>
    </div>
  );
};

// --- Main JavaFlashCardApp Component ---
const JavaFlashCardApp = () => {
  const [cards, setCards] = useState(() => {
    const saved = localStorage.getItem('flashcards');
    return saved ? JSON.parse(saved) : initialCardsData;
  });
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [fsrs] = useState(() => new FSRS()); // Initialize fsrs instance once
  const [showCalendar, setShowCalendar] = useState(false);
  const [showAddCard, setShowAddCard] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [darkMode, setDarkMode] = useDarkMode();

  // FSRS interval settings (in minutes for Again, days for others)
  const [intervalSettings, setIntervalSettings] = useState(() => {
    const saved = localStorage.getItem('fsrsIntervalSettings');
    return saved ? JSON.parse(saved) : {
      again: 1,      // 1 minute
      hard: 1,       // 1 day
      good: 4,       // 4 days
      easy: 15       // 15 days
    };
  });

  // Save cards to localStorage whenever cards change
  useEffect(() => {
    localStorage.setItem('flashcards', JSON.stringify(cards));
  }, [cards]);

  // Get unique categories
  const getCategories = useMemo(() => {
    const categories = ['All', ...new Set(cards.map(card => card.category))];
    return categories;
  }, [cards]);

  // Filter cards based on selected category and due date
  const studyDeck = useMemo(() => {
    const now = new Date();
    return cards.filter(card => {
      const categoryMatch = selectedCategory === 'All' || card.category === selectedCategory;
      const isDue = !card.dueDate || parseISO(card.dueDate) <= now;
      return categoryMatch && isDue;
    });
  }, [cards, selectedCategory]);

  // Reset card index when study deck changes
  useEffect(() => {
    setCurrentCardIndex(0);
  }, [studyDeck.length]);

  const currentDisplayCard = studyDeck[currentCardIndex];

  const handleGrade = useCallback((cardId, grade) => {
    const now = new Date();
    setCards(prevCards =>
      prevCards.map(card =>
        card.id === cardId ? fsrs.schedule(card, grade, now) : card
      )
    );
    setCurrentCardIndex(prev => prev + 1);
  }, [fsrs]);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showCalendar || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;
      if (!currentDisplayCard) return;

      // Find the grading option based on key press
      const gradingOpt = GRADING_OPTIONS.find(opt => opt.shortcut === e.key);
      if (gradingOpt) {
        e.preventDefault(); // Prevent default browser actions for '1'-'4'
        handleGrade(currentDisplayCard.id, gradingOpt.grade);
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentDisplayCard, showCalendar, handleGrade]);

  const handleAddCard = useCallback((cardData) => {
    const newCard = {
      id: Math.max(...cards.map(c => c.id), 0) + 1,
      question: cardData.question,
      answer: cardData.answer,
      category: cardData.category,
      difficulty: 5,
      stability: 2.5,
      state: 'new',
      reviewCount: 0,
      lastReview: null,
      dueDate: null
    };
    setCards(prevCards => [...prevCards, newCard]);
  }, [cards]);

  const handleImportCSV = useCallback(() => {
    // Show format information before opening file dialog
    const proceed = window.confirm(
      'CSV Import Format:\n\n' +
      'Required columns: "question", "answer"\n' +
      'Optional column: "category"\n\n' +
      'Example:\n' +
      'question,answer,category\n' +
      '"What is a class?","A blueprint for objects","OOP"\n' +
      '"What is inheritance?","A way to create new classes based on existing ones","OOP"\n\n' +
      'Click OK to select your CSV file.'
    );

    if (!proceed) return;

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        Papa.parse(file, {
          header: true,
          complete: (results) => {
            const newCards = results.data
              .filter(row => row.question && row.answer) // Filter out empty rows
              .map((row, index) => ({
                id: Math.max(...cards.map(c => c.id), 0) + index + 1,
                question: row.question || '',
                answer: row.answer || '',
                category: row.category || 'Imported',
                difficulty: 5,
                stability: 2.5,
                state: 'new',
                reviewCount: 0,
                lastReview: null,
                dueDate: null
              }));

            if (newCards.length > 0) {
              setCards(prevCards => [...prevCards, ...newCards]);
              alert(`Successfully imported ${newCards.length} cards!`);
            } else {
              alert('No valid cards found in the CSV file. Please ensure it has "question" and "answer" columns.');
            }
          },
          error: (error) => {
            alert('Error parsing CSV file: ' + error.message);
          }
        });
      }
    };
    input.click();
  }, [cards]);

  const handleSettingsChange = useCallback((newSettings) => {
    setIntervalSettings(newSettings);
    // Here you could also save to localStorage if you want persistence
    localStorage.setItem('fsrsIntervalSettings', JSON.stringify(newSettings));
  }, []);

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    // setCurrentCardIndex(0); // Already handled by useEffect depending on studyDeck.length
  };

  // Calculate due counts by date for calendar
  const dueCountsByDate = useMemo(() => {
    const counts = {};
    cards.forEach(card => {
      if (card.dueDate) {
        const dateStr = format(parseISO(card.dueDate), 'yyyy-MM-dd');
        counts[dateStr] = (counts[dateStr] || 0) + 1;
      }
    });
    return counts;
  }, [cards]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-sky-50 to-emerald-50 dark:from-slate-900 dark:via-purple-900/20 dark:to-slate-900 text-gray-800 dark:text-slate-200 flex flex-col items-center justify-center p-4 transition-all duration-500">
      <AppHeader
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        categories={getCategories}
        onToggleCalendar={() => setShowCalendar(true)}
        onToggleDarkMode={setDarkMode}
        darkMode={darkMode}
        studyDeckLength={studyDeck.length}
        currentCardOrderInDeck={studyDeck.length > 0 ? currentCardIndex + 1 : 0}
        onAddCard={() => setShowAddCard(true)}
        onImportCSV={handleImportCSV}
        onOpenSettings={() => setShowSettings(true)}
      />

      <CardDisplayArea
        studyDeck={studyDeck}
        currentCardIndex={currentCardIndex}
        handleSwipe={handleGrade}
      />

      <SwipeInstructionsSidebar show={!!currentDisplayCard} />
      <SwipeInstructionFooter show={!!currentDisplayCard} />

      <CalendarModal
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
        dueCountsByDate={dueCountsByDate}
      />

      <AddCardModal
        isOpen={showAddCard}
        onClose={() => setShowAddCard(false)}
        onAdd={handleAddCard}
      />

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        intervalSettings={intervalSettings}
        onSettingsChange={handleSettingsChange}
      />
    </div>
  );
};

export default JavaFlashCardApp;
