import { useState, useEffect } from 'react';
import { ThumbsUp, ThumbsDown, Meh, Spark<PERSON>, ArrowLeft, ArrowRight, ArrowUp, ArrowDown } from 'lucide-react';
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, addDays } from 'date-fns';

// --- components/JavaCodeHighlighter.jsx ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Enhanced text preprocessing for better visual appeal
  const preprocessText = (text) => {
    return text
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim()

      // Add visual separators for sections
      .replace(/\b(Key\s+(?:points?|features?|benefits?|concepts?|principles?|advantages?|disadvantages?)):\s*/gi, '\n\n🔹 **$1:**\n\n')
      .replace(/\b(Examples?):\s*/gi, '\n\n💡 **$1:**\n\n')
      .replace(/\b(Important|Note|Remember|Warning):\s*/gi, '\n\n⚠️ **$1:**\n\n')
      .replace(/\b(Summary|Conclusion):\s*/gi, '\n\n📋 **$1:**\n\n')
      .replace(/\b(Definition):\s*/gi, '\n\n📖 **$1:**\n\n')
      .replace(/\b(Syntax):\s*/gi, '\n\n⚡ **$1:**\n\n')

      // Format numbered lists with better spacing
      .replace(/(\d+\.\s*)/g, '\n\n$1')

      // Format lettered lists
      .replace(/([a-zA-Z]\.\s*)/g, '\n\n$1')

      // Format bullet points
      .replace(/([-•*]\s*)/g, '\n\n$1')

      // Add spacing after sentences for better paragraph breaks
      .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2')

      // Format comparison patterns
      .replace(/\b(vs\.?|versus)\b/gi, ' 🆚 ')
      .replace(/\b(compared to|in contrast to)\b/gi, ' 🔄 ')

      // Clean up excessive newlines
      .replace(/\n\s*\n\s*\n+/g, '\n\n')
      .replace(/^\n+|\n+$/g, '');
  };

  // Simple syntax highlighting for Java keywords and common patterns
  const highlightJava = (text) => {
    // Java keywords
    const keywords = [
      'public', 'private', 'protected', 'static', 'final', 'abstract', 'class', 'interface',
      'extends', 'implements', 'import', 'package', 'void', 'int', 'String', 'boolean',
      'double', 'float', 'long', 'char', 'byte', 'short', 'if', 'else', 'for', 'while',
      'do', 'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
      'finally', 'throw', 'throws', 'new', 'this', 'super', 'null', 'true', 'false'
    ];

    let highlighted = text;

    // Highlight keywords
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(regex, `<span class="text-blue-400 font-semibold">${keyword}</span>`);
    });

    // Highlight strings
    highlighted = highlighted.replace(/"([^"]*?)"/g, '<span class="text-green-400">"$1"</span>');
    highlighted = highlighted.replace(/'([^']*?)'/g, '<span class="text-green-400">\'$1\'</span>');

    // Highlight comments
    highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');

    // Highlight numbers
    highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="text-yellow-400">$&</span>');

    // Highlight method calls and class names (capitalized words)
    highlighted = highlighted.replace(/\b[A-Z][a-zA-Z0-9]*\b/g, '<span class="text-cyan-400">$&</span>');

    return highlighted;
  };

  // Check if the content looks like code (contains common programming patterns)
  const looksLikeCode = (text) => {
    const strongCodePatterns = [
      /\bpublic\s+class\b|\bprivate\s+\w+\b|\bpublic\s+static\s+void\b/, // Java keywords in context
      /\w+\s*\([^)]*\)\s*\{/, // method definitions
      /System\.out\.println\b|System\.err\.println\b/, // Java specific
      /import\s+[\w.]+;/, // import statements
      /\bclass\s+\w+\s*\{/, // class definitions
      /\bif\s*\([^)]+\)\s*\{|\bfor\s*\([^)]+\)\s*\{|\bwhile\s*\([^)]+\)\s*\{/, // control structures
    ];
    const weakCodePatterns = [
      /\{[\s\S]*\}/,
      /;[\s]*$/m,
      /\w+\.\w+\(/,
    ];
    const hasStrongCode = strongCodePatterns.some(pattern => pattern.test(text));
    const weakIndicatorCount = weakCodePatterns.filter(pattern => pattern.test(text)).length;
    const hasMultipleWeakIndicators = weakIndicatorCount >= 2 && text.length > 50;
    return hasStrongCode || hasMultipleWeakIndicators;
  };

  const looksLikeCSS = (text) => {
    const cssIndicators = [
      /\.[\w-]+\s*\{[\s\S]*?\}/,
      /#[\w-]+\s*\{[\s\S]*?\}/,
      /@media\s*\([^)]+\)\s*\{/,
      /@import\s+["']/,
      /@keyframes\s+[\w-]+\s*\{/,
      /\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const indicatorCount = cssIndicators.filter(pattern => pattern.test(text)).length;
    const strongCSSPatterns = [
      /@media\s*\([^)]+\)/,
      /@import\s+/,
      /@keyframes\s+/,
      /\.\w+\s*\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const hasStrongCSS = strongCSSPatterns.some(pattern => pattern.test(text));
    return hasStrongCSS || indicatorCount >= 2;
  };

  const highlightCSS = (text) => {
    let highlighted = text;
    highlighted = highlighted.replace(/([.#]?[\w-]+)(\s*\{)/g, '<span class="text-cyan-400 font-semibold">$1</span>$2');
    highlighted = highlighted.replace(/([\w-]+)(\s*:\s*)/g, '<span class="text-blue-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/:\s*([^;]+)(;)/g, ': <span class="text-green-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/(@[\w-]+)/g, '<span class="text-purple-400 font-semibold">$1</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/(\d+(?:\.\d+)?)(px|em|rem|%|vh|vw|deg|s|ms)/g, '<span class="text-yellow-400">$1</span><span class="text-orange-400">$2</span>');
    return highlighted;
  };

  const isCode = looksLikeCode(code);
  const isCSS = looksLikeCSS(code);

  if (isCode) {
    const codeType = isCSS ? 'CSS' : 'Java';
    const highlightFunction = isCSS ? highlightCSS : highlightJava;

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className={`${isCSS ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700' : 'bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700'} px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg`}>
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">💻</span>
            {codeType} Code
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          <pre className="overflow-x-auto text-base font-mono leading-loose whitespace-pre-wrap h-full rounded-xl bg-black/30 p-4 shadow-inner border border-violet-700/30">
            <code
              className="text-gray-100 block"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'pre-wrap',
                lineHeight: '1.9',
                textShadow: '0 1px 2px rgba(0,0,0,0.5)'
              }}
              dangerouslySetInnerHTML={{ __html: highlightFunction(code) }}
            />
          </pre>
        </div>
      </div>
    );
  } else {
    // Regular text formatting with better typography and intelligent line breaking
    const formatTextContent = (text) => {
      // Use enhanced preprocessing for better visual appeal
      const processedText = preprocessText(text);

      const paragraphs = processedText.split(/(?:\r?\n\s*){2,}/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        // Enhanced header detection with emoji support
        const isHeader = /^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*.*\*\*:?\s*$/gi.test(cleanParagraph);
        if (isHeader) {
          const match = cleanParagraph.match(/^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*(.*?)\*\*:?\s*$/);
          if (match) {
            const [, emoji, headerText] = match;
            const getHeaderStyle = (emoji) => {
              switch (emoji) {
                case '💡': return 'from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 border-yellow-500';
                case '⚠️': return 'from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 border-red-500';
                case '📋': return 'from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-500';
                case '📖': return 'from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 border-green-500';
                case '⚡': return 'from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 border-purple-500';
                default: return 'from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 border-violet-500';
              }
            };

            return (
              <div key={pIndex} className="mb-8 last:mb-0">
                <div className={`bg-gradient-to-r ${getHeaderStyle(emoji)} rounded-xl p-5 border-l-4 shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100 flex items-center gap-4">
                    <span className="text-3xl drop-shadow-sm">{emoji}</span>
                    <span className="flex-1">{headerText.trim()}</span>
                  </h3>
                </div>
              </div>
            );
          }
        }

        // Detect numbered lists (1. ... 2. ... 3. ...)
        if (/^(\d+\. .+)(?:\n\d+\. .+)*$/ms.test(cleanParagraph)) {
          // Split by lines that start with a number and dot
          const items = cleanParagraph.split(/\n(?=\d+\. )/).map(s => s.trim()).filter(Boolean);
          return (
            <ol key={pIndex} className="list-decimal ml-6 mb-4 last:mb-0 space-y-2 text-base leading-relaxed text-slate-700 dark:text-slate-200">
              {items.map((item, i) => (
                <li key={i}>{item.replace(/^\d+\.\s*/, '')}</li>
              ))}
            </ol>
          );
        }

        const isListItem = /^(\d+\.|[a-zA-Z]\.|[-•*])\s+/.test(cleanParagraph);
        if (isListItem) {
          const match = cleanParagraph.match(/^(\d+\.|[a-zA-Z]\.|[-•*])\s+(.*)$/);
          if (match) {
            const bullet = match[1];
            const content = match[2];
            return (
              <div key={pIndex} className="mb-5 last:mb-0 group">
                <div className="flex items-start gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-50/80 to-white/80 dark:from-slate-800/50 dark:to-slate-700/50 border border-slate-200/60 dark:border-slate-600/40 shadow-sm hover:shadow-md hover:from-violet-50/80 hover:to-purple-50/80 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300">
                  <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-sm">
                      {bullet.replace(/[.*]/, '•')}
                    </span>
                  </div>
                  <div className="flex-1 pt-1">
                    <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                      {content}
                    </p>
                  </div>
                </div>
              </div>
            );
          }
        }

        // Regular paragraph with enhanced styling
        return (
          <div key={pIndex} className="mb-6 last:mb-0">
            <div className="p-5 rounded-xl bg-gradient-to-br from-slate-50/90 to-white/90 dark:from-slate-800/60 dark:to-slate-700/60 border border-slate-200/70 dark:border-slate-600/50 shadow-sm hover:shadow-md transition-all duration-300">
              <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                {cleanParagraph}
              </p>
            </div>
          </div>
        );
      }).filter(Boolean);
    };

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">📄</span>
            Document Content
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          {formatTextContent(code)}
        </div>
      </div>
    );
  }
};

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }
];

// Placeholder main app export to resolve import error
const JavaFlashcardApp = () => {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-2xl font-bold text-violet-700">Java Flashcard App Main Component Placeholder</div>
    </div>
  );
};

export default JavaFlashcardApp;