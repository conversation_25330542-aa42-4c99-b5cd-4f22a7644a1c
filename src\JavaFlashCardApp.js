import { useState, useEffect } from 'react';
import { ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown } from 'lucide-react';
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, addDays } from 'date-fns';

// --- components/JavaCodeHighlighter.jsx ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Enhanced text preprocessing for better visual appeal
  const preprocessText = (text) => {
    return text
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim()

      // Add visual separators for sections
      .replace(/\b(Key\s+(?:points?|features?|benefits?|concepts?|principles?|advantages?|disadvantages?)):\s*/gi, '\n\n🔹 **$1:**\n\n')
      .replace(/\b(Examples?):\s*/gi, '\n\n💡 **$1:**\n\n')
      .replace(/\b(Important|Note|Remember|Warning):\s*/gi, '\n\n⚠️ **$1:**\n\n')
      .replace(/\b(Summary|Conclusion):\s*/gi, '\n\n📋 **$1:**\n\n')
      .replace(/\b(Definition):\s*/gi, '\n\n📖 **$1:**\n\n')
      .replace(/\b(Syntax):\s*/gi, '\n\n⚡ **$1:**\n\n')
      .replace(/\b(Implementation|Code):\s*/gi, '\n\n💻 **$1:**\n\n')
      .replace(/\b(Usage|How to use):\s*/gi, '\n\n🎯 **$1:**\n\n')
      .replace(/\b(Best practices|Tips):\s*/gi, '\n\n✨ **$1:**\n\n')

      // Format numbered lists with better spacing
      .replace(/(\d+\.\s*)/g, '\n\n$1')

      // Format lettered lists
      .replace(/([a-zA-Z]\.\s*)/g, '\n\n$1')

      // Format bullet points
      .replace(/([-•*]\s*)/g, '\n\n$1')

      // Add spacing after sentences for better paragraph breaks
      .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2')

      // Format comparison patterns
      .replace(/\b(vs\.?|versus)\b/gi, ' 🆚 ')
      .replace(/\b(compared to|in contrast to)\b/gi, ' 🔄 ')

      // Clean up excessive newlines
      .replace(/\n\s*\n\s*\n+/g, '\n\n')
      .replace(/^\n+|\n+$/g, '');
  };

  // Enhanced Java syntax highlighting
  const highlightJava = (text) => {
    // Java keywords with semantic grouping
    const keywords = {
      access: ['public', 'private', 'protected', 'default'],
      modifiers: ['static', 'final', 'abstract', 'synchronized', 'volatile', 'transient', 'native'],
      types: ['class', 'interface', 'enum', 'void', 'int', 'String', 'boolean', 'double', 'float', 'long', 'char', 'byte', 'short'],
      control: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default', 'break', 'continue', 'return'],
      exception: ['try', 'catch', 'finally', 'throw', 'throws'],
      oop: ['extends', 'implements', 'import', 'package', 'new', 'this', 'super'],
      literals: ['null', 'true', 'false']
    };

    let highlighted = text;

    // Highlight keywords with different colors based on their category
    Object.entries(keywords).forEach(([category, words]) => {
      const color = {
        access: 'text-purple-400',
        modifiers: 'text-blue-400',
        types: 'text-cyan-400',
        control: 'text-pink-400',
        exception: 'text-orange-400',
        oop: 'text-green-400',
        literals: 'text-yellow-400'
      }[category];

      words.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g');
        highlighted = highlighted.replace(regex, `<span class="${color} font-semibold">${keyword}</span>`);
      });
    });

    // Highlight strings with gradient effect
    highlighted = highlighted.replace(/"([^"]*?)"/g, '<span class="text-green-400 bg-green-900/20 px-1 rounded">"$1"</span>');
    highlighted = highlighted.replace(/'([^']*?)'/g, '<span class="text-green-400 bg-green-900/20 px-1 rounded">\'$1\'</span>');

    // Highlight comments with italic style
    highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');

    // Highlight numbers with subtle background
    highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="text-yellow-400 bg-yellow-900/20 px-1 rounded">$&</span>');

    // Highlight method calls and class names
    highlighted = highlighted.replace(/\b([A-Z][a-zA-Z0-9]*)\b/g, '<span class="text-cyan-400 font-medium">$1</span>');

    // Highlight method parameters
    highlighted = highlighted.replace(/\(([^)]*)\)/g, (match, params) => {
      return `(<span class="text-orange-300">${params}</span>)`;
    });

    return highlighted;
  };

  // Check if the content looks like code (contains common programming patterns)
  const looksLikeCode = (text) => {
    const strongCodePatterns = [
      /\bpublic\s+class\b|\bprivate\s+\w+\b|\bpublic\s+static\s+void\b/, // Java keywords in context
      /\w+\s*\([^)]*\)\s*\{/, // method definitions
      /System\.out\.println\b|System\.err\.println\b/, // Java specific
      /import\s+[\w.]+;/, // import statements
      /\bclass\s+\w+\s*\{/, // class definitions
      /\bif\s*\([^)]+\)\s*\{|\bfor\s*\([^)]+\)\s*\{|\bwhile\s*\([^)]+\)\s*\{/, // control structures
    ];
    const weakCodePatterns = [
      /\{[\s\S]*\}/,
      /;[\s]*$/m,
      /\w+\.\w+\(/,
    ];
    const hasStrongCode = strongCodePatterns.some(pattern => pattern.test(text));
    const weakIndicatorCount = weakCodePatterns.filter(pattern => pattern.test(text)).length;
    const hasMultipleWeakIndicators = weakIndicatorCount >= 2 && text.length > 50;
    return hasStrongCode || hasMultipleWeakIndicators;
  };

  const looksLikeCSS = (text) => {
    const cssIndicators = [
      /\.[\w-]+\s*\{[\s\S]*?\}/,
      /#[\w-]+\s*\{[\s\S]*?\}/,
      /@media\s*\([^)]+\)\s*\{/,
      /@import\s+["']/,
      /@keyframes\s+[\w-]+\s*\{/,
      /\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const indicatorCount = cssIndicators.filter(pattern => pattern.test(text)).length;
    const strongCSSPatterns = [
      /@media\s*\([^)]+\)/,
      /@import\s+/,
      /@keyframes\s+/,
      /\.\w+\s*\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const hasStrongCSS = strongCSSPatterns.some(pattern => pattern.test(text));
    return hasStrongCSS || indicatorCount >= 2;
  };

  const highlightCSS = (text) => {
    let highlighted = text;
    highlighted = highlighted.replace(/([.#]?[\w-]+)(\s*\{)/g, '<span class="text-cyan-400 font-semibold">$1</span>$2');
    highlighted = highlighted.replace(/([\w-]+)(\s*:\s*)/g, '<span class="text-blue-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/:\s*([^;]+)(;)/g, ': <span class="text-green-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/(@[\w-]+)/g, '<span class="text-purple-400 font-semibold">$1</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/(\d+(?:\.\d+)?)(px|em|rem|%|vh|vw|deg|s|ms)/g, '<span class="text-yellow-400">$1</span><span class="text-orange-400">$2</span>');
    return highlighted;
  };

  const isCode = looksLikeCode(code);
  const isCSS = looksLikeCSS(code);

  if (isCode) {
    const codeType = isCSS ? 'CSS' : 'Java';
    const highlightFunction = isCSS ? highlightCSS : highlightJava;

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className={`${isCSS ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700' : 'bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700'} px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg`}>
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">💻</span>
            {codeType} Code
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          <pre className="overflow-x-auto text-base font-mono leading-loose whitespace-pre h-full rounded-xl bg-black/30 p-4 shadow-inner border border-violet-700/30">
            <code
              className="text-gray-100 block"
              style={{
                wordBreak: 'keep-all',
                overflowWrap: 'normal',
                whiteSpace: 'pre',
                lineHeight: '1.9',
                textShadow: '0 1px 2px rgba(0,0,0,0.5)'
              }}
              dangerouslySetInnerHTML={{ __html: highlightFunction(code) }}
            />
          </pre>
        </div>
      </div>
    );
  } else {
    // Regular text formatting with better typography and intelligent line breaking
    const formatTextContent = (text) => {
      // Use enhanced preprocessing for better visual appeal
      const processedText = preprocessText(text);

      const paragraphs = processedText.split(/(?:\r?\n\s*){2,}/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        // Enhanced header detection with emoji support
        const isHeader = /^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*.*\*\*:?\s*$/gi.test(cleanParagraph);
        if (isHeader) {
          const match = cleanParagraph.match(/^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*(.*?)\*\*:?\s*$/);
          if (match) {
            const [, emoji, headerText] = match;
            const getHeaderStyle = (emoji) => {
              switch (emoji) {
                case '💡': return 'from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 border-yellow-500';
                case '⚠️': return 'from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 border-red-500';
                case '📋': return 'from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-500';
                case '📖': return 'from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 border-green-500';
                case '⚡': return 'from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 border-purple-500';
                default: return 'from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 border-violet-500';
              }
            };

            return (
              <div key={pIndex} className="mb-8 last:mb-0">
                <div className={`bg-gradient-to-r ${getHeaderStyle(emoji)} rounded-xl p-5 border-l-4 shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100 flex items-center gap-4">
                    <span className="text-3xl drop-shadow-sm">{emoji}</span>
                    <span className="flex-1">{headerText.trim()}</span>
                  </h3>
                </div>
              </div>
            );
          }
        }

        // Detect numbered lists (1. ... 2. ... 3. ...)
        if (/^(\d+\. .+)(?:\n\d+\. .+)*$/ms.test(cleanParagraph)) {
          // Split by lines that start with a number and dot
          const items = cleanParagraph.split(/\n(?=\d+\. )/).map(s => s.trim()).filter(Boolean);
          return (
            <ol key={pIndex} className="list-decimal ml-6 mb-4 last:mb-0 space-y-2 text-base leading-relaxed text-slate-700 dark:text-slate-200">
              {items.map((item, i) => (
                <li key={i}>{item.replace(/^\d+\.\s*/, '')}</li>
              ))}
            </ol>
          );
        }

        const isListItem = /^(\d+\.|[a-zA-Z]\.|[-•*])\s+/.test(cleanParagraph);
        if (isListItem) {
          const match = cleanParagraph.match(/^(\d+\.|[a-zA-Z]\.|[-•*])\s+(.*)$/);
          if (match) {
            const bullet = match[1];
            const content = match[2];
            return (
              <div key={pIndex} className="mb-5 last:mb-0 group">
                <div className="flex items-start gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-50/80 to-white/80 dark:from-slate-800/50 dark:to-slate-700/50 border border-slate-200/60 dark:border-slate-600/40 shadow-sm hover:shadow-md hover:from-violet-50/80 hover:to-purple-50/80 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300">
                  <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-sm">
                      {bullet.replace(/[.*]/, '•')}
                    </span>
                  </div>
                  <div className="flex-1 pt-1">
                    <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                      {content}
                    </p>
                  </div>
                </div>
              </div>
            );
          }
        }

        // Regular paragraph with enhanced styling
        return (
          <div key={pIndex} className="mb-6 last:mb-0">
            <div className="p-5 rounded-xl bg-gradient-to-br from-slate-50/90 to-white/90 dark:from-slate-800/60 dark:to-slate-700/60 border border-slate-200/70 dark:border-slate-600/50 shadow-sm hover:shadow-md transition-all duration-300">
              <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                {cleanParagraph}
              </p>
            </div>
          </div>
        );
      }).filter(Boolean);
    };

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">📄</span>
            Document Content
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          {formatTextContent(code)}
        </div>
      </div>
    );
  }
};

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }
];

// --- Sample Flashcard Data ---
const INITIAL_CARDS = [
  {
    id: 1,
    question: "What is a class in Java?",
    answer: "A class is a blueprint or template for creating objects. It defines the structure and behavior that objects of that type will have.\n\nKey features:\n1. Encapsulates data (fields) and methods\n2. Serves as a template for object creation\n3. Supports inheritance and polymorphism\n\nExample:\nclass Car {\n    String brand;\n    void start() {\n        System.out.println(\"Car started\");\n    }\n}",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 2,
    question: "What is the difference between == and .equals() in Java?",
    answer: "== compares references (memory addresses) while .equals() compares actual content.\n\nKey differences:\n1. == checks if two references point to the same object\n2. .equals() checks if two objects have the same value\n3. For primitives, == compares values directly\n4. For objects, == compares memory addresses\n\nExample:\nString a = new String(\"hello\");\nString b = new String(\"hello\");\nSystem.out.println(a == b);        // false\nSystem.out.println(a.equals(b));   // true",
    category: "Basic Concepts",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 3,
    question: "What are the main principles of Object-Oriented Programming?",
    answer: "The four main principles of OOP are:\n\n1. Encapsulation - Bundling data and methods together, hiding internal details\n2. Inheritance - Creating new classes based on existing ones\n3. Polymorphism - Objects of different types responding to the same interface\n4. Abstraction - Hiding complex implementation details behind simple interfaces\n\nBenefits:\n• Code reusability\n• Modularity\n• Maintainability\n• Scalability",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  }
];

// --- Main Flashcard App Component ---
const JavaFlashcardApp = () => {
  const [cards, setCards] = useState(INITIAL_CARDS);
  const [currentIdx, setCurrentIdx] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [feedback, setFeedback] = useState(null);

  useEffect(() => {
    setShowAnswer(false);
    setFeedback(null);
  }, [currentIdx]);

  // Keyboard shortcuts for grading
  useEffect(() => {
    const handleKey = (e) => {
      if (e.repeat) return;
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;
      if (!showAnswer && (e.key === ' ' || e.key === 'Enter')) {
        e.preventDefault();
        setShowAnswer(true);
      } else if (showAnswer) {
        const opt = GRADING_OPTIONS.find(o => o.shortcut === e.key);
        if (opt) {
          e.preventDefault();
          handleGrade(opt.grade);
        }
      }
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [showAnswer, cards, currentIdx]);

  if (!cards.length) {
    return <div className="flex items-center justify-center min-h-screen text-2xl font-bold text-violet-700">No cards available.</div>;
  }

  const card = cards[currentIdx];

  function handleGrade(grade) {
    setFeedback(GRADING_OPTIONS.find(opt => opt.grade === grade));
    setTimeout(() => {
      setFeedback(null);
      setShowAnswer(false);
      setCurrentIdx(idx => (idx + 1) % cards.length);
    }, 600);
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-violet-50 to-purple-100 dark:from-slate-900 dark:to-slate-950 p-4">
      <div className="w-full max-w-xl mx-auto">
        <div className="mb-6">
          <span className="text-xs px-3 py-1.5 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full font-semibold shadow-md">{card.category}</span>
          <span className="ml-2 text-xs text-slate-400 dark:text-slate-500 bg-slate-100/50 dark:bg-slate-700/50 px-2 py-1 rounded-lg">
            S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
          </span>
        </div>
        <div className="mt-4 min-h-[200px]">
          {showAnswer ? (
            <div className="flex flex-col md:flex-row gap-6 items-stretch">
              <div className="flex-1 flex flex-col justify-center">
                <div className="text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none whitespace-pre-wrap break-words leading-relaxed pb-4 border-b border-slate-200 dark:border-slate-700 md:border-b-0 md:border-r md:pr-6 md:mb-0">
                  {card.question}
                </div>
              </div>
              <div className="flex-1 min-w-[320px] max-w-xl">
                <JavaCodeHighlighter code={card.answer} />
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowAnswer(true)}
              className="w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white py-4 rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 font-bold text-lg shadow-xl hover:shadow-2xl transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 dark:ring-offset-slate-900"
              aria-label="Show Answer (Space)"
            >
              ✨ Show Answer <span className="ml-2 text-xs text-purple-200 bg-white/20 px-2 py-1 rounded-full">[Space]</span>
            </button>
          )}
        </div>
        {showAnswer && (
          <div className="grid grid-cols-2 gap-3 mt-6">
            {GRADING_OPTIONS.map(opt => (
              <button
                key={opt.grade}
                className={`bg-gradient-to-r ${
                  opt.color === 'red' ? 'from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700' :
                  opt.color === 'amber' ? 'from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700' :
                  opt.color === 'emerald' ? 'from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700' :
                  'from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700'
                } text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50`}
                onClick={() => handleGrade(opt.grade)}
                aria-label={`${opt.label} (${opt.shortcut})`}
              >
                <opt.arrowIcon className="inline h-4 w-4 mr-1.5" />
                {opt.label}
                <span className="ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[{opt.shortcut}]</span>
              </button>
            ))}
          </div>
        )}
        {feedback && (
          <div className={`fixed inset-0 flex items-center justify-center z-50 pointer-events-none transition-opacity duration-300 ${feedback ? 'opacity-100' : 'opacity-0'}`}>
            <div className={`p-8 rounded-2xl shadow-2xl text-3xl font-bold flex flex-col items-center gap-2 bg-white/90 dark:bg-slate-900/90 ${feedback.feedbackColor}`}>
              <feedback.icon size={48} />
              <span>{feedback.label}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default JavaFlashcardApp;