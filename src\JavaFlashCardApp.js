import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON>, CheckCircle, Filter, X, CalendarDays, Zap, Plus, Upload, ChevronDown, Settings,
  ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown
} from 'lucide-react';
import <PERSON> from 'papaparse';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css'; // Default styling for react-calendar
import { useDrag } from 'react-use-gesture';
import { animated, useSpring } from '@react-spring/web';
import { format, parseISO, addDays } from 'date-fns';

// --- Initial Cards Data ---
const initialCardsData = [
  {
    id: 1,
    question: "What is a class in Java?",
    answer: "A class is a blueprint or template for creating objects. It defines the structure and behavior that objects of that type will have.\n\nKey features:\n1. Encapsulates data (fields) and methods\n2. Serves as a template for object creation\n3. Supports inheritance and polymorphism\n\nExample:\nclass Car {\n    String brand;\n    void start() {\n        System.out.println(\"Car started\");\n    }\n}",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 2,
    question: "What is the difference between == and .equals() in Java?",
    answer: "== compares references (memory addresses) while .equals() compares actual content.\n\nKey differences:\n1. == checks if two references point to the same object\n2. .equals() checks if two objects have the same value\n3. For primitives, == compares values directly\n4. For objects, == compares memory addresses\n\nExample:\nString a = new String(\"hello\");\nString b = new String(\"hello\");\nSystem.out.println(a == b);        // false\nSystem.out.println(a.equals(b));   // true",
    category: "Basic Concepts",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 3,
    question: "What are the main principles of Object-Oriented Programming?",
    answer: "The four main principles of OOP are:\n\n1. Encapsulation - Bundling data and methods together, hiding internal details\n2. Inheritance - Creating new classes based on existing ones\n3. Polymorphism - Objects of different types responding to the same interface\n4. Abstraction - Hiding complex implementation details behind simple interfaces\n\nBenefits:\n• Code reusability\n• Modularity\n• Maintainability\n• Scalability",
    category: "OOP",
    difficulty: 5,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  }
];

// --- utils/fsrs.js ---
// FSRS Algorithm Implementation (No changes to core logic, conceptually moved)
class FSRS {
  constructor() {
    this.w = [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61]; // Default weights
  }
  initStability(grade) { return Math.max(this.w[grade - 1], 0.1); }
  initDifficulty(grade) { return Math.min(Math.max(this.w[4] - this.w[5] * (grade - 3), 1), 10); }
  nextInterval(stability) {
    const requestRetention = 0.9;
    return Math.max(1, Math.round(stability * Math.log(requestRetention) / Math.log(0.5)));
  }
  nextStability(difficulty, stability, retrievability, grade) {
    const hardPenalty = grade === 2 ? this.w[15] : 1;
    const easyBonus = grade === 4 ? this.w[16] : 1;
    if (grade === 1) {
      return this.w[11] * Math.pow(difficulty, -this.w[12]) * (Math.pow(stability + 1, this.w[13]) - 1) * Math.exp((1 - retrievability) * this.w[14]);
    } else {
      return stability * (1 + Math.exp(this.w[8]) * (11 - difficulty) * Math.pow(stability, -this.w[9]) * (Math.exp((1 - retrievability) * this.w[10]) - 1)) * hardPenalty * easyBonus;
    }
  }
  nextDifficulty(difficulty, grade) {
    const newDifficulty = difficulty - this.w[6] * (grade - 3);
    return Math.min(Math.max(newDifficulty, 1), 10);
  }
  forgettingCurve(elapsedDays, stability) { return Math.pow(1 + elapsedDays / (9 * stability), -1); }
  schedule(card, grade, reviewDate = new Date()) {
    const elapsedDays = card.lastReview ? (reviewDate.getTime() - new Date(card.lastReview).getTime()) / (1000 * 60 * 60 * 24) : 0;
    let { difficulty, stability } = card;
    if (card.state === 'new') {
      difficulty = this.initDifficulty(grade);
      stability = this.initStability(grade);
    } else {
      const retrievability = this.forgettingCurve(elapsedDays, stability);
      difficulty = this.nextDifficulty(difficulty, grade);
      stability = this.nextStability(difficulty, stability, retrievability, grade);
    }
    const interval = this.nextInterval(stability);
    const dueDate = addDays(reviewDate, interval);
    return {
      ...card,
      difficulty: Math.round(difficulty * 100) / 100,
      stability: Math.round(stability * 100) / 100,
      interval,
      dueDate: format(dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      lastReview: format(reviewDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      reviewCount: (card.reviewCount || 0) + 1,
      state: grade === 1 ? 'learning' : (grade === 2 ? 'relearning' : 'review')
    };
  }
}

// --- data/initialCards.js ---
// (Conceptually moved - using the enhanced initial data above)


// --- components/JavaCodeHighlighter.jsx ---// --- components/JavaCodeHighlighter.jsx ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Simple syntax highlighting for Java keywords and common patterns
  const highlightJava = (text) => {
    // Java keywords
    const keywords = [
      'public', 'private', 'protected', 'static', 'final', 'abstract', 'class', 'interface',
      'extends', 'implements', 'import', 'package', 'void', 'int', 'String', 'boolean',
      'double', 'float', 'long', 'char', 'byte', 'short', 'if', 'else', 'for', 'while',
      'do', 'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
      'finally', 'throw', 'throws', 'new', 'this', 'super', 'null', 'true', 'false'
    ];

    let highlighted = text;

    // Highlight keywords
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(regex, `<span class="text-blue-400 font-semibold">${keyword}</span>`);
    });

    // Highlight strings
    highlighted = highlighted.replace(/"([^"]*?)"/g, '<span class="text-green-400">"$1"</span>');
    highlighted = highlighted.replace(/'([^']*?)'/g, '<span class="text-green-400">\'$1\'</span>');

    // Highlight comments
    highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');

    // Highlight numbers
    highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="text-yellow-400">$&</span>');

    // Highlight method calls and class names (capitalized words)
    highlighted = highlighted.replace(/\b[A-Z][a-zA-Z0-9]*\b/g, '<span class="text-cyan-400">$&</span>');

    return highlighted;
  };

  // Check if the content looks like code (contains common programming patterns)
  const looksLikeCode = (text) => {
    const strongCodePatterns = [
      /\bpublic\s+class\b|\bprivate\s+\w+\b|\bpublic\s+static\s+void\b/, // Java keywords in context
      /\w+\s*\([^)]*\)\s*\{/, // method definitions
      /System\.out\.println\b|System\.err\.println\b/, // Java specific
      /import\s+[\w.]+;/, // import statements
      /\bclass\s+\w+\s*\{/, // class definitions
      /\bif\s*\([^)]+\)\s*\{|\bfor\s*\([^)]+\)\s*\{|\bwhile\s*\([^)]+\)\s*\{/, // control structures
    ];
    const weakCodePatterns = [
      /\{[\s\S]*\}/,
      /;[\s]*$/m,
      /\w+\.\w+\(/,
    ];
    const hasStrongCode = strongCodePatterns.some(pattern => pattern.test(text));
    const weakIndicatorCount = weakCodePatterns.filter(pattern => pattern.test(text)).length;
    const hasMultipleWeakIndicators = weakIndicatorCount >= 2 && text.length > 50;
    return hasStrongCode || hasMultipleWeakIndicators;
  };

  const looksLikeCSS = (text) => {
    const cssIndicators = [
      /\.[\w-]+\s*\{[\s\S]*?\}/,
      /#[\w-]+\s*\{[\s\S]*?\}/,
      /@media\s*\([^)]+\)\s*\{/,
      /@import\s+["']/,
      /@keyframes\s+[\w-]+\s*\{/,
      /\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const indicatorCount = cssIndicators.filter(pattern => pattern.test(text)).length;
    const strongCSSPatterns = [
      /@media\s*\([^)]+\)/,
      /@import\s+/,
      /@keyframes\s+/,
      /\.\w+\s*\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const hasStrongCSS = strongCSSPatterns.some(pattern => pattern.test(text));
    return hasStrongCSS || indicatorCount >= 2;
  };

  const highlightCSS = (text) => {
    let highlighted = text;
    highlighted = highlighted.replace(/([.#]?[\w-]+)(\s*\{)/g, '<span class="text-cyan-400 font-semibold">$1</span>$2');
    highlighted = highlighted.replace(/([\w-]+)(\s*:\s*)/g, '<span class="text-blue-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/:\s*([^;]+)(;)/g, ': <span class="text-green-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/(@[\w-]+)/g, '<span class="text-purple-400 font-semibold">$1</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/(\d+(?:\.\d+)?)(px|em|rem|%|vh|vw|deg|s|ms)/g, '<span class="text-yellow-400">$1</span><span class="text-orange-400">$2</span>');
    return highlighted;
  };

  const isCode = looksLikeCode(code);
  const isCSS = looksLikeCSS(code);

  if (isCode) {
    const codeType = isCSS ? 'CSS' : 'Java';
    const highlightFunction = isCSS ? highlightCSS : highlightJava;

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className={`${isCSS ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700' : 'bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700'} px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg`}>
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">💻</span>
            {codeType} Code
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          <pre className="overflow-x-auto text-base font-mono leading-loose whitespace-pre-wrap h-full rounded-xl bg-black/30 p-4 shadow-inner border border-violet-700/30">
            <code
              className="text-gray-100 block"
              style={{
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                whiteSpace: 'pre-wrap',
                lineHeight: '1.9',
                textShadow: '0 1px 2px rgba(0,0,0,0.5)'
              }}
              dangerouslySetInnerHTML={{ __html: highlightFunction(code) }}
            />
          </pre>
        </div>
      </div>
    );
  } else {
    // Regular text formatting with better typography and intelligent line breaking
    const formatTextContent = (text) => {
      // Pre-process text to ensure numbered lists and bullet points are properly separated
      let processedText = text
        // Normalize multi-spaces within lines, but preserve newlines for now.
        .replace(/ +/g, ' ')
        .trim()
        // Add proper spacing around numbered lists
        .replace(/(\d+\.\s*)/g, '\n\n$1')
        // Add proper spacing around lettered lists
        .replace(/([a-zA-Z]\.\s*)/g, '\n\n$1')
        // Add proper spacing around bullet points
        .replace(/([-•*]\s*)/g, '\n\n$1')
        // Add spacing after sentences for better paragraph breaks
        .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2')
        // Format common headers with bold styling
        .replace(/(Key\s+(?:points?|features?|benefits?|concepts?|principles?)):\s*/gi, '\n\n**$1:**\n\n')
        .replace(/(Examples?):\s*/gi, '\n\n**$1:**\n\n')
        .replace(/(Note|Important|Remember):\s*/gi, '\n\n**💡 $1:**\n\n')
        // Clean up excessive newlines
        .replace(/\n\s*\n\s*\n+/g, '\n\n')
        .replace(/^\n+|\n+$/g, '');

      const paragraphs = processedText.split(/(?:\r?\n\s*){2,}/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        const isHeader = /^\*\*.*\*\*:?$/gi.test(cleanParagraph);
        if (isHeader) {
          const headerText = cleanParagraph.replace(/^\*\*|\*\*:?\s*$/g, '');
          return (
            <div key={pIndex} className="mb-6 last:mb-0"> {/* Consistent bottom margin */}
              <div className="bg-gradient-to-r from-violet-100 to-purple-100 dark:from-slate-800/70 dark:to-purple-900/50 rounded-lg p-4 border-l-4 border-violet-500 dark:border-purple-500 shadow-md">
                <h3 className="text-lg md:text-xl font-semibold text-violet-700 dark:text-violet-200 flex items-center gap-3">
                  {headerText.includes('💡') ? (
                    <>
                      <span className="text-xl">💡</span>
                      <span>{headerText.replace('💡 ', '')}</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2.5 h-2.5 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full shadow-sm"></div>
                      <span>{headerText}</span>
                    </>
                  )}
                </h3>
              </div>
            </div>
          );
        }

        // Detect numbered lists (1. ... 2. ... 3. ...)
        if (/^(\d+\. .+)(?:\n\d+\. .+)*$/ms.test(cleanParagraph)) {
          // Split by lines that start with a number and dot
          const items = cleanParagraph.split(/\n(?=\d+\. )/).map(s => s.trim()).filter(Boolean);
          return (
            <ol key={pIndex} className="list-decimal ml-6 mb-4 last:mb-0 space-y-2 text-base leading-relaxed text-slate-700 dark:text-slate-200">
              {items.map((item, i) => (
                <li key={i}>{item.replace(/^\d+\.\s*/, '')}</li>
              ))}
            </ol>
          );
        }

        const isListItem = /^(\d+\.|[a-zA-Z]\.|[-•*])\s+/.test(cleanParagraph);
        if (isListItem) {
          const match = cleanParagraph.match(/^(\d+\.|[a-zA-Z]\.|[-•*])\s+(.*)$/);
          if (match) {
            const bullet = match[1];
            const content = match[2];
            return (
              <div key={pIndex} className="mb-4 last:mb-0">
                <div className="flex items-start gap-3">
                  <div className="text-violet-500 dark:text-violet-400 text-lg leading-tight">
                    {bullet}
                  </div>
                  <div className="flex-1 text-slate-700 dark:text-slate-200 text-base leading-relaxed">
                    {content}
                  </div>
                </div>
              </div>
            );
          }
        }

        // Regular paragraph
        return (
          <p key={pIndex} className="mb-4 last:mb-0 text-slate-700 dark:text-slate-200 text-base leading-relaxed">
            {cleanParagraph}
          </p>
        );
      }).filter(Boolean);
    };

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">📄</span>
            Document Content
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          {formatTextContent(content)}
        </div>
      </div>
    );
  }
};

export default JavaCodeHighlighter;